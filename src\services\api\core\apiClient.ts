import { toast } from 'sonner';

// Token request and response interfaces
export interface TokenRequest {
  phoneNumber: string;
  deviceToken: string;
}

export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}

export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  pageCount: number;
  currentPage: number;
  pageSize: number;
}

// Token management
let isRefreshingToken = false;
let refreshTokenPromise: Promise<string> | null = null;

// Function to generate a new token
export async function generateToken(): Promise<string> {
  if (isRefreshingToken) {
    // If a token refresh is already in progress, return the existing promise
    if (refreshTokenPromise) {
      return refreshTokenPromise;
    }
  }

  isRefreshingToken = true;
  
  // Create a new promise for the token refresh
  refreshTokenPromise = new Promise<string>(async (resolve, reject) => {
    try {
      const tokenRequest: TokenRequest = {
        phoneNumber: "9481922603",
        deviceToken: "123"
      };

      const url = '/api/Auth/token';
      console.log('Generating token with request:', tokenRequest);

      // Include Content-Type header for POST request
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tokenRequest)
      });

      console.log('Token request status:', response.status, response.statusText);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.message || response.statusText || 'Failed to generate token';
        throw new Error(errorMessage);
      }

      const tokenData: TokenResponse = await response.json();
      console.log('Token response data:', tokenData);

      // Store both access token and refresh token
      localStorage.setItem('accessToken', tokenData.accessToken);
      localStorage.setItem('refreshToken', tokenData.refreshToken);
      
      // Log the stored token for debugging
      console.log('Stored access token:', localStorage.getItem('accessToken'));

      resolve(tokenData.accessToken);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to generate token';
      toast.error(message);
      reject(error);
    } finally {
      isRefreshingToken = false;
      refreshTokenPromise = null;
    }
  });

  return refreshTokenPromise;
}

// Check if token is expired
export function isTokenExpired(): boolean {
  const accessToken = localStorage.getItem('accessToken');
  return !accessToken;
}

// Create request headers with authentication token if it exists
export function getHeaders(includeAuth: boolean = true): Record<string, string> {
  // Start with an empty headers object
  const headers: Record<string, string> = {};

  if (includeAuth) {
    // Get the token from localStorage
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      // Make sure we're using the exact format that works in Postman
      headers['Authorization'] = `Bearer ${accessToken}`;
      console.log('Using authorization header:', headers['Authorization']);
    } else {
      console.warn('No access token found in localStorage');
    }
  }

  return headers;
}

// Build query string for pagination and sorting
export function buildQueryString(params: PaginationParams): string {
  const queryParams = new URLSearchParams();
  
  queryParams.append('page', params.page.toString());
  queryParams.append('pageSize', params.pageSize.toString());
  
  if (params.sortBy) {
    queryParams.append('sortBy', params.sortBy);
  }
  
  if (params.sortDirection) {
    queryParams.append('sortDirection', params.sortDirection);
  }
  
  return queryParams.toString();
}

// Helper function to handle API responses
export async function handleResponse<T>(response: Response): Promise<T> {
  // Clone the response so we can read the text and still use it for JSON parsing
  const clonedResponse = response.clone();
  const rawText = await clonedResponse.text();
  console.log('Raw API Response:', rawText);
  
  if (!response.ok) {
    let errorMessage = response.statusText || 'An error occurred';
    try {
      const errorData = JSON.parse(rawText);
      errorMessage = errorData.message || errorMessage;
    } catch (e) {
      console.error('Error parsing error response:', e);
      // If we can't parse the error as JSON, use the raw text
      errorMessage = rawText || errorMessage;
    }
    throw new Error(errorMessage);
  }

  try {
    // If the response is empty, return an empty object or array
    if (!rawText.trim()) {
      console.warn('Empty response received from server');
      return (Array.isArray({})) ? [] as unknown as T : {} as unknown as T;
    }
    
    // Try to parse the JSON response
    const parsedData = JSON.parse(rawText) as T;
    console.log('Successfully parsed JSON response');
    return parsedData;
  } catch (e) {
    console.error('Error parsing JSON response:', e);
    console.error('Raw response text:', rawText);
    throw new Error('Invalid JSON response from server: ' + rawText.substring(0, 100) + '...');
  }
}

// Generic API request function with token refresh capability
export async function apiRequest<T>(
  endpoint: string,
  method: string = 'GET',
  data?: unknown,
  requiresAuth: boolean = true
): Promise<T> {
  // Construct the full URL
  const url = `${endpoint}`;
  console.log(`Making ${method} request to: ${url}`);
  
  // For debugging, also log the absolute URL
  const absoluteUrl = new URL(url, window.location.origin).href;
  console.log(`Absolute URL: ${absoluteUrl}`);

  // Check if token is needed and expired
  if (requiresAuth && isTokenExpired()) {
    console.log('Token expired, generating new token...');
    // Generate a new token before proceeding
    await generateToken();
  }

  try {
    const headers = getHeaders(requiresAuth);
    console.log('Request headers:', headers);
    
    if (data) {
      console.log('Request body:', JSON.stringify(data));
    }

    // Create request options
    const options: RequestInit = {
      method,
      headers
    };
    
    // Only add the body if there's data to send
    if (data) {
      // For POST requests, we need to explicitly set the Content-Type header
      if (method === 'POST') {
        options.headers = {
          ...options.headers,
          'Content-Type': 'application/json'
        };
      }
      options.body = JSON.stringify(data);
    }
    
    console.log('Request options:', options);
    
    const response = await fetch(url, options);
    
    console.log(`Response status: ${response.status} ${response.statusText}`);
    console.log('Response headers:', Object.fromEntries([...response.headers.entries()]));

    // If unauthorized and token is required, try refreshing the token and retry
    if (response.status === 401 && requiresAuth) {
      console.log('Unauthorized response, refreshing token and retrying...');
      // Generate a new token
      await generateToken();
      
      // Retry the request with the new token
      const retryHeaders = getHeaders(requiresAuth);
      console.log('Retry request headers:', retryHeaders);
      
      // Create retry options
      const retryOptions: RequestInit = {
        method,
        headers: retryHeaders
      };
      
      // Only add the body if there's data to send
      if (data) {
        // For POST requests, we need to explicitly set the Content-Type header
        if (method === 'POST') {
          retryOptions.headers = {
            ...retryOptions.headers,
            'Content-Type': 'application/json'
          };
        }
        retryOptions.body = JSON.stringify(data);
      }
      
      console.log('Retry options:', retryOptions);
      
      const retryResponse = await fetch(url, retryOptions);
      
      console.log(`Retry response status: ${retryResponse.status} ${retryResponse.statusText}`);
      return await handleResponse<T>(retryResponse);
    }

    return await handleResponse<T>(response);
  } catch (error) {
    console.error('API request error:', error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    toast.error(message);
    throw error;
  }
}

// API operations
export const apiClient = {
  // Authentication
  login: async (username: string, password: string) => {
    // For demo purposes, we'll validate the credentials locally
    // but generate a real token from the API
    if (username !== 'admin' || password !== 'password') {
      throw new Error('Invalid credentials');
    }
    
    // Generate a token using the real API endpoint
    const accessToken = await generateToken();
    
    // Create a dummy user object since we're not getting it from the API
    const dummyUser = {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin'
    };
    
    // Store the user in localStorage
    localStorage.setItem('user', JSON.stringify(dummyUser));
    
    return {
      token: accessToken, // Keep the property name as 'token' for backward compatibility
      user: dummyUser
    };
  },
  
  // Generate or refresh token
  generateToken,

  // GET request with pagination and sorting
  getItems: async <T>(
    endpoint: string,
    pagination: PaginationParams
  ): Promise<PaginatedResponse<T>> => {
    const queryString = buildQueryString(pagination);
    return apiRequest<PaginatedResponse<T>>(`${endpoint}?${queryString}`);
  },

  // Basic GET request for a single item
  getItem: async <T>(endpoint: string, id: string | number): Promise<T> => {
    return apiRequest<T>(`${endpoint}/${id}`);
  },

  // POST request to create a new item
  createItem: async <T>(endpoint: string, data: unknown): Promise<T> => {
    return apiRequest<T>(endpoint, 'POST', data);
  },

  // PUT request to update an item
  updateItem: async <T>(endpoint: string, id: string | number, data: unknown): Promise<T> => {
    return apiRequest<T>(`${endpoint}/${id}`, 'PUT', data);
  },

  // DELETE request to delete an item
  deleteItem: async <T>(endpoint: string, id: string | number): Promise<T> => {
    return apiRequest<T>(`${endpoint}/${id}`, 'DELETE');
  },

  // Custom request for any other needs
  custom: apiRequest,
};
