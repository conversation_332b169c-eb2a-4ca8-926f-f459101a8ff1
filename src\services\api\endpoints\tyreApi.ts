import { apiRequest } from '../core/apiClient';
import { Tyre, CreateTyreRequest } from '../models/tyre';

// Tyre-specific API methods
export const tyreApi = {
  // Get all tyres
  getAllTyres: async (): Promise<Tyre[]> => {
    return apiRequest<Tyre[]>('/api/Tyre/GetAllTyres');
  },

  // Create a new tyre
  createTyre: async (tyreData: CreateTyreRequest): Promise<Tyre> => {
    return apiRequest<Tyre>('/api/Tyre/Create', 'POST', tyreData);
  },

  // Delete a tyre
  deleteTyre: async (id: number): Promise<void> => {
    return apiRequest<void>(`/api/Tyre/Delete?id=${id}`, 'DELETE');
  }
};
