
import React from 'react';
import { 
  <PERSON>alog, DialogContent, DialogDescription, DialogFooter, 
  DialogHeader, DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PricingRule } from './RuleBadge';
import { PriceItemEditor } from './PriceItemEditor';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Vendor {
  id: number;
  name: string;
  status: 'approved' | 'pending' | 'rejected';
}

interface ServicePrice {
  serviceId: number;
  serviceName: string;
  basePrice: number;
  vendorPrice: number;
  taxRate: number;
  pricingRules: PricingRule[];
}

interface TyrePrice {
  tyreTypeId: number;
  tyreTypeName: string;
  basePrice: number;
  vendorPrice: number;
  taxRate: number;
  pricingRules: PricingRule[];
}

interface VendorPricingDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  vendors: Vendor[];
  selectedVendorId: number | null;
  onVendorSelect: (vendorId: string) => void;
  servicePrices: ServicePrice[];
  tyrePrices: TyrePrice[];
  onServicePriceChange: (serviceId: number, price: number) => void;
  onTyrePriceChange: (tyreTypeId: number, price: number) => void;
  onTaxRateChange: (itemId: number, taxRate: number, itemType: 'service' | 'tyre') => void;
  onPricingRuleToggle: (itemId: number, ruleId: number, itemType: 'service' | 'tyre', checked: boolean) => void;
  onOpenNewRuleDialog: (itemType: 'service' | 'tyre', id: number, name: string) => void;
  onSave: () => void;
  pricingRules: PricingRule[];
}

export const VendorPricingDialog: React.FC<VendorPricingDialogProps> = ({
  isOpen,
  onOpenChange,
  vendors,
  selectedVendorId,
  onVendorSelect,
  servicePrices,
  tyrePrices,
  onServicePriceChange,
  onTyrePriceChange,
  onTaxRateChange,
  onPricingRuleToggle,
  onOpenNewRuleDialog,
  onSave,
  pricingRules
}) => {
  const handleSave = () => {
    onSave();
    onOpenChange(false); // Make sure dialog closes after saving
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Set Vendor Pricing</DialogTitle>
          <DialogDescription>
            Configure custom pricing for services and tyre types for each vendor.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4 flex-1 min-h-0">
          <div className="mb-4">
            <Label htmlFor="vendor-select">Select Vendor</Label>
            <Select
              value={selectedVendorId?.toString() || ""}
              onValueChange={onVendorSelect}
            >
              <SelectTrigger id="vendor-select">
                <SelectValue placeholder="Select a vendor" />
              </SelectTrigger>
              <SelectContent>
                {vendors.map(vendor => (
                  <SelectItem key={vendor.id} value={vendor.id.toString()}>
                    {vendor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {selectedVendorId && (
            <ScrollArea className="h-[50vh] pr-4">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Service Pricing</h3>
                  <div className="space-y-4">
                    {servicePrices.map(service => (
                      <PriceItemEditor
                        key={service.serviceId}
                        itemType="service"
                        itemId={service.serviceId}
                        itemName={service.serviceName}
                        basePrice={service.basePrice}
                        vendorPrice={service.vendorPrice}
                        taxRate={service.taxRate}
                        pricingRules={service.pricingRules}
                        availableRules={pricingRules}
                        onVendorPriceChange={(price) => onServicePriceChange(service.serviceId, price)}
                        onTaxRateChange={(taxRate) => onTaxRateChange(service.serviceId, taxRate, 'service')}
                        onPricingRuleToggle={(ruleId, checked) => 
                          onPricingRuleToggle(service.serviceId, ruleId, 'service', checked)
                        }
                        onAddNewRule={() => onOpenNewRuleDialog('service', service.serviceId, service.serviceName)}
                      />
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-4">Tyre Pricing</h3>
                  <div className="space-y-4">
                    {tyrePrices.map(tyre => (
                      <PriceItemEditor
                        key={tyre.tyreTypeId}
                        itemType="tyre"
                        itemId={tyre.tyreTypeId}
                        itemName={tyre.tyreTypeName}
                        basePrice={tyre.basePrice}
                        vendorPrice={tyre.vendorPrice}
                        taxRate={tyre.taxRate}
                        pricingRules={tyre.pricingRules}
                        availableRules={pricingRules}
                        onVendorPriceChange={(price) => onTyrePriceChange(tyre.tyreTypeId, price)}
                        onTaxRateChange={(taxRate) => onTaxRateChange(tyre.tyreTypeId, taxRate, 'tyre')}
                        onPricingRuleToggle={(ruleId, checked) => 
                          onPricingRuleToggle(tyre.tyreTypeId, ruleId, 'tyre', checked)
                        }
                        onAddNewRule={() => onOpenNewRuleDialog('tyre', tyre.tyreTypeId, tyre.tyreTypeName)}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </ScrollArea>
          )}
        </div>
        
        <DialogFooter className="mt-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!selectedVendorId}>
            Save Pricing
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
