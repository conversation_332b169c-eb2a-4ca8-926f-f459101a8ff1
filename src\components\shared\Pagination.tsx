
import React from 'react';
import {
  Pagination as UIPagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  onPageChange: (page: number) => void;
  showSummary?: boolean;
  className?: string;
  maxVisiblePages?: number;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  pageSize,
  totalCount,
  onPageChange,
  showSummary = true,
  className = "",
  maxVisiblePages = 5
}) => {
  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    
    // Handle cases where we have fewer pages than maxVisiblePages
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
      return { pages: pageNumbers, showStartEllipsis: false, showEndEllipsis: false };
    }
    
    // Calculate start and end pages
    let startPage: number;
    let endPage: number;
    const offset = Math.floor(maxVisiblePages / 2);
    
    if (currentPage <= offset + 1) {
      // Near the start
      startPage = 1;
      endPage = maxVisiblePages - 1;
      return { 
        pages: Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i),
        showStartEllipsis: false,
        showEndEllipsis: true
      };
    } else if (currentPage >= totalPages - offset) {
      // Near the end
      startPage = totalPages - maxVisiblePages + 2;
      endPage = totalPages;
      return { 
        pages: Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i),
        showStartEllipsis: true,
        showEndEllipsis: false
      };
    } else {
      // In the middle
      startPage = currentPage - offset + 1;
      endPage = currentPage + offset - 1;
      return { 
        pages: Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i),
        showStartEllipsis: true,
        showEndEllipsis: true
      };
    }
  };

  const { pages, showStartEllipsis, showEndEllipsis } = getPageNumbers();

  return (
    <div className={`flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 ${className}`}>
      {showSummary && (
        <div className="text-sm text-muted-foreground">
          Showing {(currentPage - 1) * pageSize + 1} to {
            Math.min(currentPage * pageSize, totalCount)
          } of {totalCount || 0} entries
        </div>
      )}
      
      <UIPagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
            />
          </PaginationItem>
          
          {showStartEllipsis && (
            <>
              <PaginationItem>
                <PaginationLink onClick={() => onPageChange(1)}>1</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            </>
          )}
          
          {pages.map(num => (
            <PaginationItem key={num}>
              <PaginationLink
                onClick={() => onPageChange(num)}
                isActive={currentPage === num}
                className="cursor-pointer"
              >
                {num}
              </PaginationLink>
            </PaginationItem>
          ))}
          
          {showEndEllipsis && (
            <>
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
              <PaginationItem>
                <PaginationLink onClick={() => onPageChange(totalPages)}>
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            </>
          )}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
              className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
            />
          </PaginationItem>
        </PaginationContent>
      </UIPagination>
    </div>
  );
};
