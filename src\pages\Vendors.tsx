
import React, { useState, useEffect } from 'react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, MoreHorizontal, FileEdit, Trash2 } from 'lucide-react';
import { vendorApi } from '@/services/api/endpoints/vendorApi';
import { Vendor as VendorType, CreateVendorRequest, Location } from '@/services/api/models/vendor';
import { toast } from 'sonner';
import { PageSizeSelector } from '@/components/shared/PageSizeSelector';
import { Pagination } from '@/components/shared/Pagination';
import { Skeleton } from '@/components/ui/skeleton';
// These components are used directly in the JSX
import { VendorConfirmDelete } from '@/components/vendors/VendorConfirmDelete';

// Using the Vendor type from dotnetApi.ts
type Vendor = VendorType;

interface NewVendor {
  name: string;
  email: string;
  contactNumber: string;
  serviceDescription: string;
  location: string; // Store as string for form input
  isActive: boolean;
}

interface EditVendor {
  id: number;
  name: string;
  email: string;
  contactNumber: string;
  serviceDescription: string;
  location: string; // Store as string for form input
  isActive: boolean;
}

const Vendors: React.FC = () => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // New vendor form state
  const [newVendor, setNewVendor] = useState<NewVendor>({
    name: '',
    email: '',
    contactNumber: '',
    serviceDescription: '',
    location: '0,0', // Default location as latitude,longitude
    isActive: true
  });

  // Edit vendor form state
  const [editVendor, setEditVendor] = useState<EditVendor>({
    id: 0,
    name: '',
    email: '',
    contactNumber: '',
    serviceDescription: '',
    location: '0,0', // Default location as latitude,longitude
    isActive: true
  });

  useEffect(() => {
    const fetchVendors = async () => {
      try {
        setIsLoading(true);
        // Call the real API endpoint
        const data = await vendorApi.getAllVendors();

        setVendors(data);
      } catch (error) {
        console.error('Error fetching vendors:', error);
        toast.error('Failed to fetch vendors');
      } finally {
        setIsLoading(false);
      }
    };

    fetchVendors();
  }, []);

  // Helper function to format location object as string
  const formatLocation = (location: Location | null): string => {
    if (!location) return '0,0';
    return `${location.latitude},${location.longitude}`;
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const filteredVendors = vendors.filter(vendor => {
    // Format location as string for searching
    const locationStr = formatLocation(vendor.location);

    return (
      vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.contactNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      locationStr.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.serviceDescription.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredVendors.length / pageSize);
  const paginatedVendors = filteredVendors.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  useEffect(() => {
    // If current page is now empty due to filtering or deletion, go back to last page with data
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [totalPages, currentPage]);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddVendor = async () => {
    if (!newVendor.name || !newVendor.email || !newVendor.contactNumber || !newVendor.location) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setIsSubmitting(true);

      // Parse location string into latitude and longitude
      const [latitude, longitude] = newVendor.location.split(',').map(Number);

      const vendorData: CreateVendorRequest = {
        name: newVendor.name,
        email: newVendor.email,
        contactNumber: newVendor.contactNumber,
        serviceDescription: newVendor.serviceDescription,
        location: {
          latitude: isNaN(latitude) ? 0 : latitude,
          longitude: isNaN(longitude) ? 0 : longitude
        },
        isActive: newVendor.isActive
      };

      // Call the API to create a new vendor
      await vendorApi.createVendor(vendorData);

      // Refresh the vendor list
      const updatedVendors = await vendorApi.getAllVendors();
      setVendors(updatedVendors);

      // Reset form
      setNewVendor({
        name: '',
        email: '',
        contactNumber: '',
        serviceDescription: '',
        location: '0,0', // Default location as latitude,longitude
        isActive: true
      });

      setIsAddDialogOpen(false);
      toast.success('Vendor added successfully');
    } catch (error) {
      console.error('Error adding vendor:', error);
      toast.error('Failed to add vendor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditVendor = async () => {
    if (!editVendor.name || !editVendor.email || !editVendor.contactNumber || !editVendor.location) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setIsSubmitting(true);

      // Note: Since there's no update endpoint in the requirements,
      // we'll just update the local state for now
      // In a real app, you would call an update API endpoint here

      // Parse location string into latitude and longitude
      const [latitude, longitude] = editVendor.location.split(',').map(Number);

      const updatedVendors = vendors.map(vendor =>
        vendor.id === editVendor.id ? {
          ...vendor,
          name: editVendor.name,
          email: editVendor.email,
          contactNumber: editVendor.contactNumber,
          serviceDescription: editVendor.serviceDescription,
          location: {
            latitude: isNaN(latitude) ? 0 : latitude,
            longitude: isNaN(longitude) ? 0 : longitude
          },
          isActive: editVendor.isActive
        } : vendor
      );

      setVendors(updatedVendors);
      setIsEditDialogOpen(false);
      toast.success('Vendor updated successfully');
    } catch (error) {
      console.error('Error updating vendor:', error);
      toast.error('Failed to update vendor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteVendor = async () => {
    if (!selectedVendor) return;

    try {
      setIsSubmitting(true);

      // Call the API to delete the vendor
      await vendorApi.deleteVendor(selectedVendor.id);

      // Update local state
      const updatedVendors = vendors.filter(vendor => vendor.id !== selectedVendor.id);
      setVendors(updatedVendors);
      setIsDeleteDialogOpen(false);
      toast.success('Vendor deleted successfully');
    } catch (error) {
      console.error('Error deleting vendor:', error);
      toast.error('Failed to delete vendor');
    } finally {
      setIsSubmitting(false);
    }
  };



  const openEditDialog = (vendor: Vendor) => {
    setEditVendor({
      id: vendor.id,
      name: vendor.name,
      email: vendor.email,
      contactNumber: vendor.contactNumber,
      serviceDescription: vendor.serviceDescription,
      location: formatLocation(vendor.location),
      isActive: vendor.isActive
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (vendor: Vendor) => {
    setSelectedVendor(vendor);
    setIsDeleteDialogOpen(true);
  };

  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const getStatusBadge = (isActive: boolean, isOnline?: boolean) => {
    if (isActive) {
      if (isOnline) {
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Online</Badge>;
      }
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Active</Badge>;
    }
    return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Inactive</Badge>;
  };

  const handleNewVendorChange = (field: keyof NewVendor, value: any) => {
    setNewVendor(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditVendorChange = (field: keyof EditVendor, value: any) => {
    setEditVendor(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Vendors</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Vendor
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Vendor</DialogTitle>
              <DialogDescription>
                Add a new vendor to the system.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={newVendor.name}
                  onChange={(e) => handleNewVendorChange('name', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newVendor.email}
                  onChange={(e) => handleNewVendorChange('email', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="contactNumber">Contact Number</Label>
                <Input
                  id="contactNumber"
                  value={newVendor.contactNumber}
                  onChange={(e) => handleNewVendorChange('contactNumber', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="serviceDescription">Service Description</Label>
                <Input
                  id="serviceDescription"
                  value={newVendor.serviceDescription}
                  onChange={(e) => handleNewVendorChange('serviceDescription', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={newVendor.location}
                  onChange={(e) => handleNewVendorChange('location', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="isActive">Status</Label>
                <Select
                  value={newVendor.isActive ? "active" : "inactive"}
                  onValueChange={(value) => handleNewVendorChange('isActive', value === "active" ? true : false)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddVendor} disabled={isSubmitting}>
                {isSubmitting ? 'Adding...' : 'Add Vendor'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="relative flex-1 w-full">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search vendors..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>

        <PageSizeSelector
          pageSize={pageSize}
          onPageSizeChange={handlePageSizeChange}
          options={[5, 10, 25, 50]}
          className="min-w-fit"
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableCaption>A list of all vendors.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Contact Number</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Rating</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  <TableCell><Skeleton className="h-5 w-10" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8 rounded-full" /></TableCell>
                </TableRow>
              ))
            ) : paginatedVendors.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-10">
                  No vendors found.
                </TableCell>
              </TableRow>
            ) : (
              paginatedVendors.map((vendor) => (
                <TableRow key={vendor.id}>
                  <TableCell>{vendor.id}</TableCell>
                  <TableCell>{vendor.name}</TableCell>
                  <TableCell>{vendor.email}</TableCell>
                  <TableCell>{vendor.contactNumber}</TableCell>
                  <TableCell>{formatLocation(vendor.location)}</TableCell>
                  <TableCell>{getStatusBadge(vendor.isActive, vendor.isOnline)}</TableCell>
                  <TableCell>{vendor.averageRating.toFixed(1)} ({vendor.totalReviews})</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(vendor)}>
                          <FileEdit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openDeleteDialog(vendor)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {!isLoading && filteredVendors.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalCount={filteredVendors.length}
          onPageChange={setCurrentPage}
        />
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Vendor</DialogTitle>
            <DialogDescription>
              Update vendor information.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={editVendor.name}
                onChange={(e) => handleEditVendorChange('name', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editVendor.email}
                onChange={(e) => handleEditVendorChange('email', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-contactNumber">Contact Number</Label>
              <Input
                id="edit-contactNumber"
                value={editVendor.contactNumber}
                onChange={(e) => handleEditVendorChange('contactNumber', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-serviceDescription">Service Description</Label>
              <Input
                id="edit-serviceDescription"
                value={editVendor.serviceDescription}
                onChange={(e) => handleEditVendorChange('serviceDescription', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-location">Location</Label>
              <Input
                id="edit-location"
                value={editVendor.location}
                onChange={(e) => handleEditVendorChange('location', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-isActive">Status</Label>
              <Select
                value={editVendor.isActive ? "active" : "inactive"}
                onValueChange={(value) => handleEditVendorChange('isActive', value === "active" ? true : false)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditVendor} disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      {selectedVendor && (
        <VendorConfirmDelete
          isOpen={isDeleteDialogOpen}
          onOpenChange={() => setIsDeleteDialogOpen(false)}
          onConfirm={handleDeleteVendor}
          vendorName={selectedVendor.name}
          isSubmitting={isSubmitting}
        />
      )}
    </div>
  );
};

export default Vendors;
