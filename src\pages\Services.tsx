import React, { useState, useEffect } from 'react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, MoreHorizontal, FileEdit, Trash2 } from 'lucide-react';
import { serviceApi } from '@/services/api/endpoints/serviceApi';
import { Service as ServiceType, CreateServiceRequest } from '@/services/api/models/service';
import { toast } from 'sonner';
import { PageSizeSelector } from '@/components/shared/PageSizeSelector';
import { Pagination } from '@/components/shared/Pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

// Using the Service type from dotnetApi.ts
type Service = ServiceType;

interface NewService {
  serviceType: string;
  serviceDetails: string;
}

interface EditService {
  id: number;
  serviceType: string;
  serviceDetails: string;
}

const Services: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // New service form state
  const [newService, setNewService] = useState<NewService>({
    serviceType: '',
    serviceDetails: ''
  });

  // Edit service form state
  const [editService, setEditService] = useState<EditService>({
    id: 0,
    serviceType: '',
    serviceDetails: ''
  });

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setIsLoading(true);
        // Call the real API endpoint
        const data = await serviceApi.getAllServices();

        setServices(data);
      } catch (error) {
        console.error('Error fetching services:', error);
        toast.error('Failed to fetch services');
      } finally {
        setIsLoading(false);
      }
    };

    fetchServices();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const filteredServices = services.filter(service => {
    return (
      service.serviceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.serviceDetails.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredServices.length / pageSize);
  const paginatedServices = filteredServices.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handleNewServiceChange = (field: keyof NewService, value: string) => {
    setNewService(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditServiceChange = (field: keyof EditService, value: string) => {
    setEditService(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddService = async () => {
    if (!newService.serviceType || !newService.serviceDetails) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setIsSubmitting(true);

      const serviceData: CreateServiceRequest = {
        serviceType: newService.serviceType,
        serviceDetails: newService.serviceDetails
      };

      // Call the API to create a new service
      await serviceApi.createService(serviceData);

      // Refresh the service list
      const updatedServices = await serviceApi.getAllServices();
      setServices(updatedServices);

      // Reset form
      setNewService({
        serviceType: '',
        serviceDetails: ''
      });

      setIsAddDialogOpen(false);
      toast.success('Service added successfully');
    } catch (error) {
      console.error('Error adding service:', error);
      toast.error('Failed to add service');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditService = async () => {
    if (!editService.serviceType || !editService.serviceDetails) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setIsSubmitting(true);

      // Note: Since there's no update endpoint in the requirements,
      // we'll just update the local state for now
      // In a real app, you would call an update API endpoint here

      const updatedServices = services.map(service =>
        service.id === editService.id ? {
          ...service,
          serviceType: editService.serviceType,
          serviceDetails: editService.serviceDetails
        } : service
      );

      setServices(updatedServices);
      setIsEditDialogOpen(false);
      toast.success('Service updated successfully');
    } catch (error) {
      console.error('Error updating service:', error);
      toast.error('Failed to update service');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteService = async () => {
    if (!selectedService) return;

    try {
      setIsSubmitting(true);

      // Call the API to delete the service
      await serviceApi.deleteService(selectedService.id);

      // Update local state
      const updatedServices = services.filter(service => service.id !== selectedService.id);
      setServices(updatedServices);
      setIsDeleteDialogOpen(false);
      toast.success('Service deleted successfully');
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (service: Service) => {
    setEditService({
      id: service.id,
      serviceType: service.serviceType,
      serviceDetails: service.serviceDetails
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (service: Service) => {
    setSelectedService(service);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Services</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Service
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Service</DialogTitle>
              <DialogDescription>
                Add a new service to the system.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="serviceType">Service Type</Label>
                <Input
                  id="serviceType"
                  value={newService.serviceType}
                  onChange={(e) => handleNewServiceChange('serviceType', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="serviceDetails">Service Details</Label>
                <Input
                  id="serviceDetails"
                  value={newService.serviceDetails}
                  onChange={(e) => handleNewServiceChange('serviceDetails', e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleAddService} disabled={isSubmitting}>
                {isSubmitting ? 'Adding...' : 'Add Service'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center py-4">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search services..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="ml-auto flex items-center gap-2">
          <PageSizeSelector
            pageSize={pageSize}
            onPageSizeChange={setPageSize}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableCaption>A list of all services.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">ID</TableHead>
              <TableHead>Service Type</TableHead>
              <TableHead>Service Details</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  <TableCell><Skeleton className="h-5 w-10" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8 rounded-full" /></TableCell>
                </TableRow>
              ))
            ) : paginatedServices.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-10">
                  No services found.
                </TableCell>
              </TableRow>
            ) : (
              paginatedServices.map((service) => (
                <TableRow key={service.id}>
                  <TableCell>{service.id}</TableCell>
                  <TableCell>{service.serviceType}</TableCell>
                  <TableCell>{service.serviceDetails}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(service)}>
                          <FileEdit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openDeleteDialog(service)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}

      {/* Edit Service Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Service</DialogTitle>
            <DialogDescription>
              Make changes to the service details.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-serviceType">Service Type</Label>
              <Input
                id="edit-serviceType"
                value={editService.serviceType}
                onChange={(e) => handleEditServiceChange('serviceType', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-serviceDetails">Service Details</Label>
              <Input
                id="edit-serviceDetails"
                value={editService.serviceDetails}
                onChange={(e) => handleEditServiceChange('serviceDetails', e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleEditService} disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Service Confirmation */}
      {selectedService && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete the service "{selectedService.serviceType}".
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteService} disabled={isSubmitting}>
                {isSubmitting ? 'Deleting...' : 'Delete'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
};

export default Services;
