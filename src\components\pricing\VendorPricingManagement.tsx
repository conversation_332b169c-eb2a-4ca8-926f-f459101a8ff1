import React, { useState } from 'react';
import { Search, Plus, Edit, Trash2, ArrowLeft } from 'lucide-react';
import PricingRulesAccordion from './PricingRulesAccordion';

// Mock data
const mockVendors = [
  { id: 1, name: "PunctureFix Experts" },
  { id: 2, name: "Quick Tyre Service" },
  { id: 3, name: "Mobile Repair Pro" },
  { id: 4, name: "City Tyre Solutions" }
];

const mockPricingEntities = [
  {
    id: 1,
    name: "Standard Cleaning Service",
    basePrice: 500.00,
    platformCommissionPercentage: 15.00,
    ownerMargin: { isPercentage: true, value: 10.00, applyOwnerMargin: true }
  },
  {
    id: 2,
    name: "Premium Tyre Repair",
    basePrice: 300.00,
    platformCommissionPercentage: 10.00,
    ownerMargin: { isPercentage: false, value: 50.00, applyOwnerMargin: true }
  },
  {
    id: 3,
    name: "Fuel Delivery",
    basePrice: 80.00,
    platformCommissionPercentage: 8.00,
    ownerMargin: { isPercentage: true, value: 15.00, applyOwnerMargin: false }
  }
];

const mockServices = [
  { id: 1, name: "On-Site Tyre Service" },
  { id: 2, name: "Emergency Repair" },
  { id: 3, name: "Fuel Delivery" },
  { id: 4, name: "Battery Jump Start" }
];

const mockTyres = [
  { id: 1, brand: "MRF", type: "Tubeless" },
  { id: 2, brand: "Bridgestone", type: "Radial" },
  { id: 3, brand: "Michelin", type: "All-Season" },
  { id: 4, brand: "Apollo", type: "Sports" }
];

const mockVendorPricingData = [
  {
    id: 1,
    vendorId: 1,
    vendorName: "PunctureFix Experts",
    pricingId: 2,
    pricingName: "Premium Tyre Repair",
    vendorSpecificPrice: 280.00,
    serviceId: 1,
    serviceName: "On-Site Tyre Service",
    tyreId: 1,
    tyreBrand: "MRF",
    tyreType: "Tubeless",
    pricingRules: {
      tieredPricing: [{ minQuantity: 1, maxQuantity: 3, discountPercentage: 5.00 }],
      subscriptionPricing: [{ planType: 1, discountPercentage: 10.00 }],
      promoCodes: [{ code: "WELCOME100", discountAmount: 100.00, expiryDate: "2025-07-31" }],
      locationPricing: [{ region: "Metro", additionalCharge: 25.00 }],
      timeBasedPricing: [{ startTime: "07:00", endTime: "10:00", surgeMultiplier: 1.3 }],
      userSpecificPricing: [{ planType: 2, durationInDays: 90, discountPercentage: 15.00 }]
    }
  },
  {
    id: 2,
    vendorId: 2,
    vendorName: "Quick Tyre Service",
    pricingId: 1,
    pricingName: "Standard Cleaning Service",
    vendorSpecificPrice: 450.00,
    serviceId: 2,
    serviceName: "Emergency Repair",
    tyreId: 2,
    tyreBrand: "Bridgestone",
    tyreType: "Radial",
    pricingRules: {
      tieredPricing: [{ minQuantity: 1, maxQuantity: 5, discountPercentage: 3.00 }],
      subscriptionPricing: [{ planType: 1, discountPercentage: 8.00 }],
      promoCodes: [{ code: "QUICK20", discountAmount: 20.00, expiryDate: "2025-08-15" }],
      locationPricing: [{ region: "Urban", additionalCharge: 15.00 }],
      timeBasedPricing: [{ startTime: "18:00", endTime: "21:00", surgeMultiplier: 1.5 }],
      userSpecificPricing: [{ planType: 1, durationInDays: 30, discountPercentage: 5.00 }]
    }
  }
];

interface VendorPricing {
  id?: number;
  vendorId: number;
  vendorName: string;
  pricingId: number;
  pricingName: string;
  vendorSpecificPrice: number;
  serviceId: number;
  serviceName: string;
  tyreId: number;
  tyreBrand: string;
  tyreType: string;
  pricingRules: {
    tieredPricing: Array<{ minQuantity: number; maxQuantity: number; discountPercentage: number }>;
    subscriptionPricing: Array<{ planType: number; discountPercentage: number }>;
    promoCodes: Array<{ code: string; discountAmount: number; expiryDate: string }>;
    locationPricing: Array<{ region: string; additionalCharge: number }>;
    timeBasedPricing: Array<{ startTime: string; endTime: string; surgeMultiplier: number }>;
    userSpecificPricing: Array<{ planType: number; durationInDays: number; discountPercentage: number }>;
  };
}

const VendorPricingManagement: React.FC = () => {
  const [mode, setMode] = useState<'table' | 'create' | 'edit'>('table');
  const [vendorPricingData, setVendorPricingData] = useState<VendorPricing[]>(mockVendorPricingData);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [editingItem, setEditingItem] = useState<VendorPricing | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const itemsPerPage = 5;

  const [formData, setFormData] = useState<VendorPricing>({
    vendorId: 0,
    vendorName: '',
    pricingId: 0,
    pricingName: '',
    vendorSpecificPrice: 0,
    serviceId: 0,
    serviceName: '',
    tyreId: 0,
    tyreBrand: '',
    tyreType: '',
    pricingRules: {
      tieredPricing: [{ minQuantity: 1, maxQuantity: 5, discountPercentage: 0 }],
      subscriptionPricing: [{ planType: 1, discountPercentage: 0 }],
      promoCodes: [{ code: '', discountAmount: 0, expiryDate: '' }],
      locationPricing: [{ region: '', additionalCharge: 0 }],
      timeBasedPricing: [{ startTime: '08:00', endTime: '17:00', surgeMultiplier: 1.0 }],
      userSpecificPricing: [{ planType: 1, durationInDays: 30, discountPercentage: 0 }]
    }
  });

  // Get selected pricing entity details
  const selectedPricing = mockPricingEntities.find(p => p.id === formData.pricingId);

  // Filter data based on search term
  const filteredData = vendorPricingData.filter(item =>
    item.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.pricingName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

  const handleCreate = () => {
    setFormData({
      vendorId: 0,
      vendorName: '',
      pricingId: 0,
      pricingName: '',
      vendorSpecificPrice: 0,
      serviceId: 0,
      serviceName: '',
      tyreId: 0,
      tyreBrand: '',
      tyreType: '',
      pricingRules: {
        tieredPricing: [{ minQuantity: 1, maxQuantity: 5, discountPercentage: 0 }],
        subscriptionPricing: [{ planType: 1, discountPercentage: 0 }],
        promoCodes: [{ code: '', discountAmount: 0, expiryDate: '' }],
        locationPricing: [{ region: '', additionalCharge: 0 }],
        timeBasedPricing: [{ startTime: '08:00', endTime: '17:00', surgeMultiplier: 1.0 }],
        userSpecificPricing: [{ planType: 1, durationInDays: 30, discountPercentage: 0 }]
      }
    });
    setMode('create');
  };

  const handleEdit = (item: VendorPricing) => {
    setFormData(item);
    setEditingItem(item);
    setMode('edit');
  };

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this vendor pricing?')) {
      setVendorPricingData(prev => prev.filter(item => item.id !== id));
    }
  };

  const handleSave = () => {
    if (mode === 'create') {
      const newId = Math.max(...vendorPricingData.map(p => p.id || 0)) + 1;
      setVendorPricingData(prev => [...prev, { ...formData, id: newId }]);
    } else if (mode === 'edit' && editingItem) {
      setVendorPricingData(prev => prev.map(item =>
        item.id === editingItem.id ? { ...formData, id: editingItem.id } : item
      ));
    }
    setMode('table');
    setEditingItem(null);
  };

  const handleCancel = () => {
    setMode('table');
    setEditingItem(null);
  };

  const handleVendorChange = (vendorId: number) => {
    const vendor = mockVendors.find(v => v.id === vendorId);
    setFormData(prev => ({
      ...prev,
      vendorId,
      vendorName: vendor?.name || ''
    }));
  };

  const handlePricingChange = (pricingId: number) => {
    const pricing = mockPricingEntities.find(p => p.id === pricingId);
    setFormData(prev => ({
      ...prev,
      pricingId,
      pricingName: pricing?.name || ''
    }));
  };

  const handleServiceChange = (serviceId: number) => {
    const service = mockServices.find(s => s.id === serviceId);
    setFormData(prev => ({
      ...prev,
      serviceId,
      serviceName: service?.name || ''
    }));
  };

  const handleTyreChange = (tyreId: number) => {
    const tyre = mockTyres.find(t => t.id === tyreId);
    setFormData(prev => ({
      ...prev,
      tyreId,
      tyreBrand: tyre?.brand || '',
      tyreType: tyre?.type || ''
    }));
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const addPricingRuleItem = (section: keyof VendorPricing['pricingRules']) => {
    setFormData(prev => {
      const newItem = (() => {
        switch (section) {
          case 'tieredPricing':
            return { minQuantity: 1, maxQuantity: 5, discountPercentage: 0 };
          case 'subscriptionPricing':
            return { planType: 1, discountPercentage: 0 };
          case 'promoCodes':
            return { code: '', discountAmount: 0, expiryDate: '' };
          case 'locationPricing':
            return { region: '', additionalCharge: 0 };
          case 'timeBasedPricing':
            return { startTime: '08:00', endTime: '17:00', surgeMultiplier: 1.0 };
          case 'userSpecificPricing':
            return { planType: 1, durationInDays: 30, discountPercentage: 0 };
          default:
            return {};
        }
      })();

      return {
        ...prev,
        pricingRules: {
          ...prev.pricingRules,
          [section]: [...prev.pricingRules[section], newItem]
        }
      };
    });
  };

  const removePricingRuleItem = (section: keyof VendorPricing['pricingRules'], index: number) => {
    setFormData(prev => ({
      ...prev,
      pricingRules: {
        ...prev.pricingRules,
        [section]: prev.pricingRules[section].filter((_, i) => i !== index)
      }
    }));
  };

  const updatePricingRuleItem = (
    section: keyof VendorPricing['pricingRules'],
    index: number,
    field: string,
    value: any
  ) => {
    setFormData(prev => {
      const updatedSection = [...prev.pricingRules[section]];
      (updatedSection[index] as any)[field] = value;
      return {
        ...prev,
        pricingRules: {
          ...prev.pricingRules,
          [section]: updatedSection
        }
      };
    });
  };

  if (mode === 'create' || mode === 'edit') {
    return (
      <div className="p-6 max-w-6xl mx-auto">
        <div className="mb-6">
          <button
            onClick={handleCancel}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Vendor Pricing List
          </button>
          <h1 className="text-2xl font-bold text-gray-900">
            {mode === 'create' ? 'Create New Vendor Pricing' : 'Edit Vendor Pricing'}
          </h1>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <form onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
            {/* Basic Information */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vendor *
                  </label>
                  <select
                    value={formData.vendorId}
                    onChange={(e) => handleVendorChange(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value={0}>Select Vendor</option>
                    {mockVendors.map(vendor => (
                      <option key={vendor.id} value={vendor.id}>{vendor.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pricing *
                  </label>
                  <select
                    value={formData.pricingId}
                    onChange={(e) => handlePricingChange(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value={0}>Select Pricing</option>
                    {mockPricingEntities.map(pricing => (
                      <option key={pricing.id} value={pricing.id}>{pricing.name}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Pricing Summary */}
            {selectedPricing && (
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Pricing Summary (Read-only)</h2>
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Pricing Name</label>
                      <p className="text-sm text-gray-900">{selectedPricing.name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Base Price</label>
                      <p className="text-sm text-gray-900">${selectedPricing.basePrice.toFixed(2)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Platform Commission %</label>
                      <p className="text-sm text-gray-900">{selectedPricing.platformCommissionPercentage}%</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Owner Margin</label>
                      <p className="text-sm text-gray-900">
                        {selectedPricing.ownerMargin.isPercentage ?
                          `${selectedPricing.ownerMargin.value}%` :
                          `$${selectedPricing.ownerMargin.value}`
                        } ({selectedPricing.ownerMargin.applyOwnerMargin ? 'Applied' : 'Not Applied'})
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Additional Details */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Additional Details</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vendor Specific Price *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.vendorSpecificPrice}
                    onChange={(e) => setFormData(prev => ({ ...prev, vendorSpecificPrice: parseFloat(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Service *
                  </label>
                  <select
                    value={formData.serviceId}
                    onChange={(e) => handleServiceChange(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value={0}>Select Service</option>
                    {mockServices.map(service => (
                      <option key={service.id} value={service.id}>{service.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tyre *
                  </label>
                  <select
                    value={formData.tyreId}
                    onChange={(e) => handleTyreChange(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value={0}>Select Tyre</option>
                    {mockTyres.map(tyre => (
                      <option key={tyre.id} value={tyre.id}>{tyre.brand} - {tyre.type}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Pricing Rules Accordion */}
            <PricingRulesAccordion
              pricingRules={formData.pricingRules}
              expandedSections={expandedSections}
              onToggleSection={toggleSection}
              onAddItem={addPricingRuleItem}
              onRemoveItem={removePricingRuleItem}
              onUpdateItem={updatePricingRuleItem}
            />

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-gray-900">Vendor Pricing Management</h1>
          <button
            onClick={handleCreate}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create
          </button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search by vendor or pricing name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full md:w-96 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pricing Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor Specific Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Service
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tyre
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedData.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.vendorName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.pricingName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${item.vendorSpecificPrice.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.serviceName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.tyreBrand} - {item.tyreType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(item)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(item.id!)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(startIndex + itemsPerPage, filteredData.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredData.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === currentPage
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VendorPricingManagement;
