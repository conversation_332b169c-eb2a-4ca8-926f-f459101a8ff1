
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';

// Update the interface to support both direct props and formValues object
interface VendorFormProps {
  // Direct props mode
  name?: string;
  email?: string;
  phone?: string;
  status?: 'approved' | 'pending' | 'rejected';
  onNameChange?: (value: string) => void;
  onEmailChange?: (value: string) => void;
  onPhoneChange?: (value: string) => void;
  onStatusChange?: (value: string) => void;

  // Form values object mode (for VendorExample.tsx)
  formValues?: {
    name: string;
    email: string;
    phone: string;
  };
  handleInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;

  // Common props
  onSubmit: () => void;
  onCancel?: () => void;
  submitLabel?: string;
  isEdit?: boolean;
  isPending?: boolean;
  isSubmitting?: boolean;
}

export const VendorForm: React.FC<VendorFormProps> = ({
  // Direct props
  name = '',
  email = '',
  phone = '',
  status = 'pending',
  onNameChange,
  onEmailChange,
  onPhoneChange,
  onStatusChange,

  // Form values object props
  formValues,
  handleInputChange,

  // Common props
  onSubmit,
  onCancel,
  submitLabel = 'Submit',
  isEdit = false,
  isPending = false,
  isSubmitting = false
}) => {
  // Determine which mode we're using (direct props or formValues object)
  const isUsingFormValues = !!formValues && !!handleInputChange;

  // Helper for input handlers to support both modes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (isUsingFormValues && handleInputChange) {
      handleInputChange(e);
    } else {
      const { name: fieldName, value } = e.target;
      if (fieldName === 'name' && onNameChange) onNameChange(value);
      if (fieldName === 'email' && onEmailChange) onEmailChange(value);
      if (fieldName === 'phone' && onPhoneChange) onPhoneChange(value);
    }
  };

  return (
    <div className="grid gap-4 py-4">
      <div className="grid gap-2">
        <Label htmlFor={isEdit ? "edit-name" : "name"}>Name</Label>
        <Input
          id={isEdit ? "edit-name" : "name"}
          name="name"
          value={isUsingFormValues ? formValues.name : name}
          onChange={handleChange}
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor={isEdit ? "edit-email" : "email"}>Email</Label>
        <Input
          id={isEdit ? "edit-email" : "email"}
          name="email"
          type="email"
          value={isUsingFormValues ? formValues.email : email}
          onChange={handleChange}
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor={isEdit ? "edit-phone" : "phone"}>Phone</Label>
        <Input
          id={isEdit ? "edit-phone" : "phone"}
          name="phone"
          value={isUsingFormValues ? formValues.phone : phone}
          onChange={handleChange}
        />
      </div>

      {!isUsingFormValues && (
        <div className="grid gap-2">
          <Label htmlFor={isEdit ? "edit-status" : "status"}>Status</Label>
          <Select
            value={status}
            onValueChange={onStatusChange || (() => {})}
          >
            <SelectTrigger id={isEdit ? "edit-status" : "status"}>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="flex justify-end gap-2 mt-2">
        {onCancel && (
          <Button variant="outline" onClick={onCancel} type="button">
            Cancel
          </Button>
        )}
        <Button
          onClick={onSubmit}
          disabled={isPending || isSubmitting}
          type="button"
        >
          {isSubmitting ? 'Submitting...' : isPending ? 'Processing...' : submitLabel}
        </Button>
      </div>
    </div>
  );
};
