
import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface PageSizeSelectorProps {
  pageSize: number;
  onPageSizeChange: (size: number) => void;
  options?: number[];
  className?: string;
  label?: {
    before?: string;
    after?: string;
  };
}

export const PageSizeSelector: React.FC<PageSizeSelectorProps> = ({
  pageSize,
  onPageSizeChange,
  options = [5, 10, 25, 50],
  className = "",
  label = { before: "Show", after: "entries" },
}) => {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {label.before && <span>{label.before}</span>}
      <Select 
        value={pageSize.toString()} 
        onValueChange={(value) => onPageSizeChange(Number(value))}
      >
        <SelectTrigger className="w-20">
          <SelectValue placeholder={pageSize} />
        </SelectTrigger>
        <SelectContent>
          {options.map(option => (
            <SelectItem key={option} value={option.toString()}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {label.after && <span>{label.after}</span>}
    </div>
  );
};
