
import React from 'react';
import { Vendor } from '@/services/api/models/vendor';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface VendorTableProps {
  isLoading: boolean;
  vendors: Vendor[] | undefined;
  pageSize: number;
  sortBy: string;
  sortDirection: 'asc' | 'desc';
  onSortChange: (column: string) => void;
  onDelete: (id: number) => void;
  deletePending: boolean;
}

export const VendorTable: React.FC<VendorTableProps> = ({
  isLoading,
  vendors,
  pageSize,
  sortBy,
  sortDirection,
  onSortChange,
  onDelete,
  deletePending,
}) => {
  // Render sort indicator
  const renderSortIndicator = (column: string) => {
    if (sortBy !== column) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead
              className="cursor-pointer"
              onClick={() => onSortChange('name')}
            >
              Name {renderSortIndicator('name')}
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => onSortChange('email')}
            >
              Email {renderSortIndicator('email')}
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => onSortChange('phone')}
            >
              Phone {renderSortIndicator('phone')}
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => onSortChange('status')}
            >
              Status {renderSortIndicator('status')}
            </TableHead>
            <TableHead
              className="cursor-pointer"
              onClick={() => onSortChange('createdAt')}
            >
              Created {renderSortIndicator('createdAt')}
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: pageSize }).map((_, index) => (
              <TableRow key={`loading-${index}`}>
                <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                <TableCell><Skeleton className="h-5 w-48" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
              </TableRow>
            ))
          ) : vendors && vendors.length > 0 ? (
            vendors.map((vendor) => (
              <TableRow key={vendor.id}>
                <TableCell>{vendor.name}</TableCell>
                <TableCell>{vendor.email}</TableCell>
                <TableCell>{vendor.phone}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    vendor.status === 'approved' ? 'bg-green-100 text-green-800' :
                    vendor.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {vendor.status}
                  </span>
                </TableCell>
                <TableCell>{new Date(vendor.createdAt).toLocaleDateString()}</TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => onDelete(vendor.id)}
                    disabled={deletePending}
                  >
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="text-center">
                No vendors found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};
