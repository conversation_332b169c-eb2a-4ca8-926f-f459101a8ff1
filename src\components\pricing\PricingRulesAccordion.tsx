import React from 'react';
import { ChevronDown, ChevronRight, Plus, Trash2 } from 'lucide-react';

interface PricingRules {
  tieredPricing: Array<{ minQuantity: number; maxQuantity: number; discountPercentage: number }>;
  subscriptionPricing: Array<{ planType: number; discountPercentage: number }>;
  promoCodes: Array<{ code: string; discountAmount: number; expiryDate: string }>;
  locationPricing: Array<{ region: string; additionalCharge: number }>;
  timeBasedPricing: Array<{ startTime: string; endTime: string; surgeMultiplier: number }>;
  userSpecificPricing: Array<{ planType: number; durationInDays: number; discountPercentage: number }>;
}

interface PricingRulesAccordionProps {
  pricingRules: PricingRules;
  expandedSections: Record<string, boolean>;
  onToggleSection: (section: string) => void;
  onAddItem: (section: keyof PricingRules) => void;
  onRemoveItem: (section: keyof PricingRules, index: number) => void;
  onUpdateItem: (section: keyof PricingRules, index: number, field: string, value: any) => void;
}

const PricingRulesAccordion: React.FC<PricingRulesAccordionProps> = ({
  pricingRules,
  expandedSections,
  onToggleSection,
  onAddItem,
  onRemoveItem,
  onUpdateItem
}) => {
  const sections = [
    {
      key: 'tieredPricing',
      title: 'Tiered Pricing',
      description: 'Set different pricing based on quantity ranges'
    },
    {
      key: 'subscriptionPricing',
      title: 'Subscription Pricing',
      description: 'Special pricing for subscription plans'
    },
    {
      key: 'promoCodes',
      title: 'Promo Codes',
      description: 'Discount codes with expiry dates'
    },
    {
      key: 'locationPricing',
      title: 'Location Pricing',
      description: 'Additional charges based on location'
    },
    {
      key: 'timeBasedPricing',
      title: 'Time Based Pricing',
      description: 'Surge pricing for specific time periods'
    },
    {
      key: 'userSpecificPricing',
      title: 'User Specific Pricing',
      description: 'Special pricing for specific user plans'
    }
  ];

  const renderTieredPricing = () => (
    <div className="space-y-4">
      {pricingRules.tieredPricing.map((tier, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-md">
          <div className="flex-1 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Min Quantity</label>
              <input
                type="number"
                value={tier.minQuantity}
                onChange={(e) => onUpdateItem('tieredPricing', index, 'minQuantity', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Max Quantity</label>
              <input
                type="number"
                value={tier.maxQuantity}
                onChange={(e) => onUpdateItem('tieredPricing', index, 'maxQuantity', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Discount %</label>
              <input
                type="number"
                step="0.01"
                value={tier.discountPercentage}
                onChange={(e) => onUpdateItem('tieredPricing', index, 'discountPercentage', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={() => onRemoveItem('tieredPricing', index)}
            className="text-red-600 hover:text-red-800"
            disabled={pricingRules.tieredPricing.length === 1}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => onAddItem('tieredPricing')}
        className="flex items-center text-blue-600 hover:text-blue-800"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Tier
      </button>
    </div>
  );

  const renderSubscriptionPricing = () => (
    <div className="space-y-4">
      {pricingRules.subscriptionPricing.map((plan, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-md">
          <div className="flex-1 grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Plan Type</label>
              <select
                value={plan.planType}
                onChange={(e) => onUpdateItem('subscriptionPricing', index, 'planType', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={1}>Basic</option>
                <option value={2}>Standard</option>
                <option value={3}>Premium</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Discount %</label>
              <input
                type="number"
                step="0.01"
                value={plan.discountPercentage}
                onChange={(e) => onUpdateItem('subscriptionPricing', index, 'discountPercentage', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={() => onRemoveItem('subscriptionPricing', index)}
            className="text-red-600 hover:text-red-800"
            disabled={pricingRules.subscriptionPricing.length === 1}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => onAddItem('subscriptionPricing')}
        className="flex items-center text-blue-600 hover:text-blue-800"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Plan
      </button>
    </div>
  );

  const renderPromoCodes = () => (
    <div className="space-y-4">
      {pricingRules.promoCodes.map((promo, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-md">
          <div className="flex-1 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Code</label>
              <input
                type="text"
                value={promo.code}
                onChange={(e) => onUpdateItem('promoCodes', index, 'code', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., WELCOME100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Discount Amount</label>
              <input
                type="number"
                step="0.01"
                value={promo.discountAmount}
                onChange={(e) => onUpdateItem('promoCodes', index, 'discountAmount', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
              <input
                type="date"
                value={promo.expiryDate}
                onChange={(e) => onUpdateItem('promoCodes', index, 'expiryDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={() => onRemoveItem('promoCodes', index)}
            className="text-red-600 hover:text-red-800"
            disabled={pricingRules.promoCodes.length === 1}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => onAddItem('promoCodes')}
        className="flex items-center text-blue-600 hover:text-blue-800"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Promo Code
      </button>
    </div>
  );

  const renderLocationPricing = () => (
    <div className="space-y-4">
      {pricingRules.locationPricing.map((location, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-md">
          <div className="flex-1 grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Region</label>
              <input
                type="text"
                value={location.region}
                onChange={(e) => onUpdateItem('locationPricing', index, 'region', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Urban, Rural, Metro"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Additional Charge</label>
              <input
                type="number"
                step="0.01"
                value={location.additionalCharge}
                onChange={(e) => onUpdateItem('locationPricing', index, 'additionalCharge', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={() => onRemoveItem('locationPricing', index)}
            className="text-red-600 hover:text-red-800"
            disabled={pricingRules.locationPricing.length === 1}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => onAddItem('locationPricing')}
        className="flex items-center text-blue-600 hover:text-blue-800"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Location
      </button>
    </div>
  );

  const renderTimeBasedPricing = () => (
    <div className="space-y-4">
      {pricingRules.timeBasedPricing.map((timeSlot, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-md">
          <div className="flex-1 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
              <input
                type="time"
                value={timeSlot.startTime}
                onChange={(e) => onUpdateItem('timeBasedPricing', index, 'startTime', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
              <input
                type="time"
                value={timeSlot.endTime}
                onChange={(e) => onUpdateItem('timeBasedPricing', index, 'endTime', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Surge Multiplier</label>
              <input
                type="number"
                step="0.1"
                value={timeSlot.surgeMultiplier}
                onChange={(e) => onUpdateItem('timeBasedPricing', index, 'surgeMultiplier', parseFloat(e.target.value) || 1.0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={() => onRemoveItem('timeBasedPricing', index)}
            className="text-red-600 hover:text-red-800"
            disabled={pricingRules.timeBasedPricing.length === 1}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => onAddItem('timeBasedPricing')}
        className="flex items-center text-blue-600 hover:text-blue-800"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Time Slot
      </button>
    </div>
  );

  const renderUserSpecificPricing = () => (
    <div className="space-y-4">
      {pricingRules.userSpecificPricing.map((userPlan, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-md">
          <div className="flex-1 grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Plan Type</label>
              <select
                value={userPlan.planType}
                onChange={(e) => onUpdateItem('userSpecificPricing', index, 'planType', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={1}>Basic</option>
                <option value={2}>Standard</option>
                <option value={3}>Premium</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Duration (Days)</label>
              <input
                type="number"
                value={userPlan.durationInDays}
                onChange={(e) => onUpdateItem('userSpecificPricing', index, 'durationInDays', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Discount %</label>
              <input
                type="number"
                step="0.01"
                value={userPlan.discountPercentage}
                onChange={(e) => onUpdateItem('userSpecificPricing', index, 'discountPercentage', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={() => onRemoveItem('userSpecificPricing', index)}
            className="text-red-600 hover:text-red-800"
            disabled={pricingRules.userSpecificPricing.length === 1}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => onAddItem('userSpecificPricing')}
        className="flex items-center text-blue-600 hover:text-blue-800"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add User Plan
      </button>
    </div>
  );

  const renderSectionContent = (sectionKey: string) => {
    switch (sectionKey) {
      case 'tieredPricing':
        return renderTieredPricing();
      case 'subscriptionPricing':
        return renderSubscriptionPricing();
      case 'promoCodes':
        return renderPromoCodes();
      case 'locationPricing':
        return renderLocationPricing();
      case 'timeBasedPricing':
        return renderTimeBasedPricing();
      case 'userSpecificPricing':
        return renderUserSpecificPricing();
      default:
        return null;
    }
  };

  return (
    <div className="mb-8">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Pricing Rules</h2>
      <div className="space-y-4">
        {sections.map((section) => (
          <div key={section.key} className="border border-gray-200 rounded-md">
            <button
              type="button"
              onClick={() => onToggleSection(section.key)}
              className="w-full px-4 py-3 text-left flex items-center justify-between bg-gray-50 hover:bg-gray-100 rounded-t-md"
            >
              <div>
                <h3 className="font-medium text-gray-900">{section.title}</h3>
                <p className="text-sm text-gray-500">{section.description}</p>
              </div>
              {expandedSections[section.key] ? (
                <ChevronDown className="w-5 h-5 text-gray-500" />
              ) : (
                <ChevronRight className="w-5 h-5 text-gray-500" />
              )}
            </button>
            {expandedSections[section.key] && (
              <div className="p-4 border-t border-gray-200">
                {renderSectionContent(section.key)}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PricingRulesAccordion;
