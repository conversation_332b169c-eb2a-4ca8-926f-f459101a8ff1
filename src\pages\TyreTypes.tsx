import React, { useState, useEffect } from 'react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, MoreHorizontal, FileEdit, Trash2 } from 'lucide-react';
import { tyreApi } from '@/services/api/endpoints/tyreApi';
import { Tyre as TyreType, CreateTyreRequest } from '@/services/api/models/tyre';
import { toast } from 'sonner';
import { PageSizeSelector } from '@/components/shared/PageSizeSelector';
import { Pagination } from '@/components/shared/Pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

// Using the Tyre type from the models
type Tyre = TyreType;

interface NewTyre {
  tyreType: string;
  description: string;
  dimension: string;
}

interface EditTyre {
  id: number;
  tyreType: string;
  description: string;
  dimension: string;
}

const TyreTypes: React.FC = () => {
  const [tyres, setTyres] = useState<Tyre[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTyre, setSelectedTyre] = useState<Tyre | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // New tyre form state
  const [newTyre, setNewTyre] = useState<NewTyre>({
    tyreType: '',
    description: '',
    dimension: ''
  });

  // Edit tyre form state
  const [editTyre, setEditTyre] = useState<EditTyre>({
    id: 0,
    tyreType: '',
    description: '',
    dimension: ''
  });

  useEffect(() => {
    const fetchTyres = async () => {
      try {
        setIsLoading(true);
        // Call the real API endpoint
        const data = await tyreApi.getAllTyres();
        
        setTyres(data);
      } catch (error) {
        console.error('Error fetching tyre types:', error);
        toast.error('Failed to fetch tyre types');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTyres();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const filteredTyres = tyres.filter(tyre => {
    return (
      tyre.tyreType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tyre.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tyre.dimension.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredTyres.length / pageSize);
  const paginatedTyres = filteredTyres.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handleNewTyreChange = (field: keyof NewTyre, value: string) => {
    setNewTyre(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditTyreChange = (field: keyof EditTyre, value: string) => {
    setEditTyre(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddTyre = async () => {
    if (!newTyre.tyreType || !newTyre.description || !newTyre.dimension) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const tyreData: CreateTyreRequest = {
        tyreType: newTyre.tyreType,
        description: newTyre.description,
        dimension: newTyre.dimension
      };
      
      // Call the API to create a new tyre
      await tyreApi.createTyre(tyreData);
      
      // Refresh the tyre list
      const updatedTyres = await tyreApi.getAllTyres();
      setTyres(updatedTyres);
      
      // Reset form
      setNewTyre({
        tyreType: '',
        description: '',
        dimension: ''
      });
      
      setIsAddDialogOpen(false);
      toast.success('Tyre type added successfully');
    } catch (error) {
      console.error('Error adding tyre type:', error);
      toast.error('Failed to add tyre type');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditTyre = async () => {
    if (!editTyre.tyreType || !editTyre.description || !editTyre.dimension) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Note: Since there's no update endpoint in the requirements,
      // we'll just update the local state for now
      // In a real app, you would call an update API endpoint here
      
      const updatedTyres = tyres.map(tyre =>
        tyre.id === editTyre.id ? {
          ...tyre,
          tyreType: editTyre.tyreType,
          description: editTyre.description,
          dimension: editTyre.dimension
        } : tyre
      );
      
      setTyres(updatedTyres);
      setIsEditDialogOpen(false);
      toast.success('Tyre type updated successfully');
    } catch (error) {
      console.error('Error updating tyre type:', error);
      toast.error('Failed to update tyre type');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteTyre = async () => {
    if (!selectedTyre) return;
    
    try {
      setIsSubmitting(true);
      
      // Call the API to delete the tyre
      await tyreApi.deleteTyre(selectedTyre.id);
      
      // Update local state
      const updatedTyres = tyres.filter(tyre => tyre.id !== selectedTyre.id);
      setTyres(updatedTyres);
      setIsDeleteDialogOpen(false);
      toast.success('Tyre type deleted successfully');
    } catch (error) {
      console.error('Error deleting tyre type:', error);
      toast.error('Failed to delete tyre type');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (tyre: Tyre) => {
    setEditTyre({
      id: tyre.id,
      tyreType: tyre.tyreType,
      description: tyre.description,
      dimension: tyre.dimension
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (tyre: Tyre) => {
    setSelectedTyre(tyre);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Tyre Types</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Tyre Type
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Tyre Type</DialogTitle>
              <DialogDescription>
                Add a new tyre type to the system.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="tyreType">Tyre Type</Label>
                <Input 
                  id="tyreType" 
                  value={newTyre.tyreType}
                  onChange={(e) => handleNewTyreChange('tyreType', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input 
                  id="description" 
                  value={newTyre.description}
                  onChange={(e) => handleNewTyreChange('description', e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="dimension">Dimension</Label>
                <Input 
                  id="dimension" 
                  value={newTyre.dimension}
                  onChange={(e) => handleNewTyreChange('dimension', e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleAddTyre} disabled={isSubmitting}>
                {isSubmitting ? 'Adding...' : 'Add Tyre Type'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="flex items-center py-4">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search tyre types..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="ml-auto flex items-center gap-2">
          <PageSizeSelector 
            pageSize={pageSize} 
            onPageSizeChange={setPageSize} 
          />
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableCaption>A list of all tyre types.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">ID</TableHead>
              <TableHead>Tyre Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Dimension</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  <TableCell><Skeleton className="h-5 w-10" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8 rounded-full" /></TableCell>
                </TableRow>
              ))
            ) : paginatedTyres.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-10">
                  No tyre types found.
                </TableCell>
              </TableRow>
            ) : (
              paginatedTyres.map((tyre) => (
                <TableRow key={tyre.id}>
                  <TableCell>{tyre.id}</TableCell>
                  <TableCell>{tyre.tyreType}</TableCell>
                  <TableCell>{tyre.description}</TableCell>
                  <TableCell>{tyre.dimension}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(tyre)}>
                          <FileEdit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openDeleteDialog(tyre)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
      
      {/* Edit Tyre Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Tyre Type</DialogTitle>
            <DialogDescription>
              Make changes to the tyre type details.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-tyreType">Tyre Type</Label>
              <Input 
                id="edit-tyreType" 
                value={editTyre.tyreType}
                onChange={(e) => handleEditTyreChange('tyreType', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-description">Description</Label>
              <Input 
                id="edit-description" 
                value={editTyre.description}
                onChange={(e) => handleEditTyreChange('description', e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-dimension">Dimension</Label>
              <Input 
                id="edit-dimension" 
                value={editTyre.dimension}
                onChange={(e) => handleEditTyreChange('dimension', e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleEditTyre} disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Tyre Confirmation */}
      {selectedTyre && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete the tyre type "{selectedTyre.tyreType}".
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteTyre} disabled={isSubmitting}>
                {isSubmitting ? 'Deleting...' : 'Delete'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
};

export default TyreTypes;
