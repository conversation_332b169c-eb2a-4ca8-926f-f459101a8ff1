
import React, { useState, useEffect } from 'react';
import { 
  Table, TableBody, TableCaption, TableCell, TableHead, 
  TableHeader, TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle 
} from '@/components/ui/dialog';
import { Search, Plus, DollarSign, Table as TableIcon, LayoutGrid } from 'lucide-react';
import { api } from '@/services/api';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { PageSizeSelector } from '@/components/shared/PageSizeSelector';
import { useForm } from "react-hook-form";
import { RuleBadge, PricingRule } from '@/components/vendors/RuleBadge';
import { PricingRuleForm, PricingRuleFormData } from '@/components/vendors/PricingRuleForm';
import { VendorPricingDialog } from '@/components/vendors/VendorPricingDialog';

interface Vendor {
  id: number;
  name: string;
  status: 'approved' | 'pending' | 'rejected';
}

interface Service {
  id: number;
  name: string;
  price: number;
}

interface TyreType {
  id: number;
  name: string;
}

interface PriceMapping {
  id: number;
  vendorId: number;
  vendorName: string;
  servicePrices: {
    serviceId: number;
    serviceName: string;
    basePrice: number;
    vendorPrice: number;
    taxRate: number;
    pricingRules: PricingRule[];
  }[];
  tyrePrices: {
    tyreTypeId: number;
    tyreTypeName: string;
    basePrice: number;
    vendorPrice: number;
    taxRate: number;
    pricingRules: PricingRule[];
  }[];
}

const VendorPricing: React.FC = () => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [tyreTypes, setTyreTypes] = useState<TyreType[]>([]);
  const [pricingRules, setPricingRules] = useState<PricingRule[]>([]);
  const [priceMappings, setPriceMappings] = useState<PriceMapping[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedVendorId, setSelectedVendorId] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'card' | 'table'>('card');
  const [isNewRuleDialogOpen, setIsNewRuleDialogOpen] = useState(false);
  const [currentEditingItem, setCurrentEditingItem] = useState<{
    type: 'service' | 'tyre';
    id: number;
    name: string;
  } | null>(null);
  
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  
  const [servicePrices, setServicePrices] = useState<{
    serviceId: number;
    serviceName: string;
    basePrice: number;
    vendorPrice: number;
    taxRate: number;
    pricingRules: PricingRule[];
  }[]>([]);
  
  const [tyrePrices, setTyrePrices] = useState<{
    tyreTypeId: number;
    tyreTypeName: string;
    basePrice: number;
    vendorPrice: number;
    taxRate: number;
    pricingRules: PricingRule[];
  }[]>([]);
  
  const newRuleForm = useForm<PricingRuleFormData>({
    defaultValues: {
      name: "",
      type: "percentage",
      value: 0,
      condition: "",
      minAmount: 0,
      maxAmount: 0
    }
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const vendorsData = api.mockData.getVendors();
        const servicesData = api.mockData.getServices();
        const tyreTypesData = api.mockData.getTyreTypes();
        
        const typedVendors = vendorsData.map(vendor => ({
          id: vendor.id,
          name: vendor.name,
          status: vendor.status as 'approved' | 'pending' | 'rejected'
        }));
        
        setVendors(typedVendors);
        
        const simplifiedServices = servicesData.map(service => ({
          id: service.id,
          name: service.name,
          price: service.price
        }));
        
        setServices(simplifiedServices);
        
        const simplifiedTyreTypes = tyreTypesData.map(tyreType => ({
          id: tyreType.id,
          name: tyreType.name
        }));
        
        setTyreTypes(simplifiedTyreTypes);
        
        const mockPricingRules: PricingRule[] = [
          {
            id: 1,
            name: "Standard Discount",
            type: "percentage",
            value: 10, 
            condition: "Always apply"
          },
          {
            id: 2,
            name: "Premium Service Fee",
            type: "fixed",
            value: 15,
            condition: "For premium services"
          },
          {
            id: 3,
            name: "Bulk Order Discount",
            type: "percentage",
            value: 15,
            condition: "For orders above threshold",
            minAmount: 100
          },
          {
            id: 4,
            name: "New Customer Discount",
            type: "percentage",
            value: 5,
            condition: "First-time customers"
          },
          {
            id: 5,
            name: "Weekend Surcharge",
            type: "percentage",
            value: 8,
            condition: "Applied on weekends"
          }
        ];
        
        setPricingRules(mockPricingRules);
        
        const mockPriceMappings: PriceMapping[] = [
          {
            id: 1,
            vendorId: 1,
            vendorName: 'Tyre Express',
            servicePrices: [
              { 
                serviceId: 1, 
                serviceName: 'Puncture Repair', 
                basePrice: 20, 
                vendorPrice: 18,
                taxRate: 10,
                pricingRules: [mockPricingRules[0], mockPricingRules[3]]
              },
              { 
                serviceId: 2, 
                serviceName: 'Tire Replacement', 
                basePrice: 50, 
                vendorPrice: 45,
                taxRate: 10,
                pricingRules: [mockPricingRules[0]]
              }
            ],
            tyrePrices: [
              { 
                tyreTypeId: 1, 
                tyreTypeName: 'Standard Passenger', 
                basePrice: 80, 
                vendorPrice: 75,
                taxRate: 8,
                pricingRules: [mockPricingRules[2]]
              },
              { 
                tyreTypeId: 3, 
                tyreTypeName: 'Performance', 
                basePrice: 120, 
                vendorPrice: 110,
                taxRate: 8,
                pricingRules: [mockPricingRules[1], mockPricingRules[2]]
              }
            ]
          },
          {
            id: 2,
            vendorId: 3,
            vendorName: 'Road Assist',
            servicePrices: [
              { 
                serviceId: 1, 
                serviceName: 'Puncture Repair', 
                basePrice: 20, 
                vendorPrice: 22,
                taxRate: 10,
                pricingRules: [mockPricingRules[4]]
              },
              { 
                serviceId: 3, 
                serviceName: 'Wheel Alignment', 
                basePrice: 35, 
                vendorPrice: 32,
                taxRate: 10,
                pricingRules: []
              }
            ],
            tyrePrices: [
              { 
                tyreTypeId: 2, 
                tyreTypeName: 'All-Season', 
                basePrice: 90, 
                vendorPrice: 85,
                taxRate: 8,
                pricingRules: [mockPricingRules[3]]
              },
              { 
                tyreTypeId: 4, 
                tyreTypeName: 'SUV/Truck', 
                basePrice: 150, 
                vendorPrice: 140,
                taxRate: 8,
                pricingRules: []
              }
            ]
          },
          {
            id: 3,
            vendorId: 4,
            vendorName: 'Tyre Hub',
            servicePrices: [
              { 
                serviceId: 2, 
                serviceName: 'Tire Replacement', 
                basePrice: 50, 
                vendorPrice: 48,
                taxRate: 10,
                pricingRules: [mockPricingRules[0]]
              },
              { 
                serviceId: 4, 
                serviceName: 'Tyre Balancing', 
                basePrice: 25, 
                vendorPrice: 23,
                taxRate: 10,
                pricingRules: []
              }
            ],
            tyrePrices: [
              { 
                tyreTypeId: 1, 
                tyreTypeName: 'Standard Passenger', 
                basePrice: 80, 
                vendorPrice: 77,
                taxRate: 8,
                pricingRules: [mockPricingRules[2]]
              },
              { 
                tyreTypeId: 5, 
                tyreTypeName: 'Winter', 
                basePrice: 130, 
                vendorPrice: 125,
                taxRate: 8,
                pricingRules: []
              }
            ]
          },
          {
            id: 4,
            vendorId: 5,
            vendorName: 'Quick Tyres',
            servicePrices: [
              { 
                serviceId: 1, 
                serviceName: 'Puncture Repair', 
                basePrice: 20, 
                vendorPrice: 19,
                taxRate: 10,
                pricingRules: [mockPricingRules[3]]
              },
              { 
                serviceId: 5, 
                serviceName: 'Nitrogen Filling', 
                basePrice: 15, 
                vendorPrice: 14,
                taxRate: 10,
                pricingRules: []
              }
            ],
            tyrePrices: [
              { 
                tyreTypeId: 2, 
                tyreTypeName: 'All-Season', 
                basePrice: 90, 
                vendorPrice: 88,
                taxRate: 8,
                pricingRules: []
              },
              { 
                tyreTypeId: 3, 
                tyreTypeName: 'Performance', 
                basePrice: 120, 
                vendorPrice: 118,
                taxRate: 8,
                pricingRules: [mockPricingRules[1]]
              }
            ]
          }
        ];
        
        setPriceMappings(mockPriceMappings);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to fetch data');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };
  
  const filteredPriceMappings = priceMappings.filter(mapping => 
    mapping.vendorName.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const totalItems = filteredPriceMappings.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  const currentPageData = filteredPriceMappings.slice(startIndex, endIndex);
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const handleVendorSelect = (vendorId: string) => {
    const id = parseInt(vendorId);
    setSelectedVendorId(id);
    
    const existingMapping = priceMappings.find(mapping => mapping.vendorId === id);
    
    if (existingMapping) {
      setServicePrices(existingMapping.servicePrices);
      setTyrePrices(existingMapping.tyrePrices);
    } else {
      const defaultServicePrices = services.map(service => ({
        serviceId: service.id,
        serviceName: service.name,
        basePrice: service.price,
        vendorPrice: service.price,
        taxRate: 10,
        pricingRules: []
      }));
      
      const defaultTyrePrices = tyreTypes.map(tyreType => ({
        tyreTypeId: tyreType.id,
        tyreTypeName: tyreType.name,
        basePrice: 100,
        vendorPrice: 100,
        taxRate: 8,
        pricingRules: []
      }));
      
      setServicePrices(defaultServicePrices);
      setTyrePrices(defaultTyrePrices);
    }
  };
  
  const handleServicePriceChange = (serviceId: number, price: number) => {
    setServicePrices(current => 
      current.map(item => 
        item.serviceId === serviceId 
          ? { ...item, vendorPrice: price } 
          : item
      )
    );
  };
  
  const handleTyrePriceChange = (tyreTypeId: number, price: number) => {
    setTyrePrices(current => 
      current.map(item => 
        item.tyreTypeId === tyreTypeId 
          ? { ...item, vendorPrice: price } 
          : item
      )
    );
  };
  
  const handleTaxRateChange = (itemId: number, taxRate: number, itemType: 'service' | 'tyre') => {
    if (itemType === 'service') {
      setServicePrices(current => 
        current.map(item => 
          item.serviceId === itemId 
            ? { ...item, taxRate } 
            : item
        )
      );
    } else {
      setTyrePrices(current => 
        current.map(item => 
          item.tyreTypeId === itemId 
            ? { ...item, taxRate } 
            : item
        )
      );
    }
  };

  const handlePricingRuleToggle = (itemId: number, ruleId: number, itemType: 'service' | 'tyre', checked: boolean) => {
    const rule = pricingRules.find(r => r.id === ruleId);
    
    if (!rule) return;
    
    if (itemType === 'service') {
      setServicePrices(current => 
        current.map(item => {
          if (item.serviceId === itemId) {
            return checked 
              ? { 
                  ...item, 
                  pricingRules: [...item.pricingRules, rule] 
                }
              : { 
                  ...item, 
                  pricingRules: item.pricingRules.filter(r => r.id !== ruleId) 
                };
          }
          return item;
        })
      );
    } else {
      setTyrePrices(current => 
        current.map(item => {
          if (item.tyreTypeId === itemId) {
            return checked 
              ? { 
                  ...item, 
                  pricingRules: [...item.pricingRules, rule] 
                }
              : { 
                  ...item, 
                  pricingRules: item.pricingRules.filter(r => r.id !== ruleId) 
                };
          }
          return item;
        })
      );
    }
  };
  
  const handleCreateNewRule = (data: PricingRuleFormData) => {
    const newRule: PricingRule = {
      id: pricingRules.length > 0 ? Math.max(...pricingRules.map(r => r.id)) + 1 : 1,
      name: data.name,
      type: data.type,
      value: data.value,
      condition: data.condition,
      minAmount: data.minAmount > 0 ? data.minAmount : undefined,
      maxAmount: data.maxAmount > 0 ? data.maxAmount : undefined,
    };
    
    setPricingRules([...pricingRules, newRule]);
    
    if (currentEditingItem) {
      if (currentEditingItem.type === 'service') {
        setServicePrices(current => 
          current.map(item => 
            item.serviceId === currentEditingItem.id 
              ? { ...item, pricingRules: [...item.pricingRules, newRule] } 
              : item
          )
        );
      } else {
        setTyrePrices(current => 
          current.map(item => 
            item.tyreTypeId === currentEditingItem.id 
              ? { ...item, pricingRules: [...item.pricingRules, newRule] } 
              : item
          )
        );
      }
    }
    
    newRuleForm.reset();
    setIsNewRuleDialogOpen(false);
    toast.success(`Pricing rule "${data.name}" created successfully`);
  };
  
  const handleOpenNewRuleDialog = (itemType: 'service' | 'tyre', id: number, name: string) => {
    setCurrentEditingItem({ type: itemType, id, name });
    setIsNewRuleDialogOpen(true);
  };
  
  const handleSavePricing = () => {
    if (!selectedVendorId) {
      toast.error('Please select a vendor');
      return;
    }
    
    const selectedVendor = vendors.find(v => v.id === selectedVendorId);
    
    if (!selectedVendor) {
      toast.error('Selected vendor not found');
      return;
    }
    
    const existingMappingIndex = priceMappings.findIndex(m => m.vendorId === selectedVendorId);
    
    if (existingMappingIndex !== -1) {
      const updatedMappings = [...priceMappings];
      updatedMappings[existingMappingIndex] = {
        ...updatedMappings[existingMappingIndex],
        servicePrices,
        tyrePrices
      };
      
      setPriceMappings(updatedMappings);
      toast.success('Vendor pricing updated successfully');
    } else {
      const newId = priceMappings.length > 0 ? Math.max(...priceMappings.map(m => m.id)) + 1 : 1;
      
      const newMapping: PriceMapping = {
        id: newId,
        vendorId: selectedVendorId,
        vendorName: selectedVendor.name,
        servicePrices,
        tyrePrices
      };
      
      setPriceMappings([...priceMappings, newMapping]);
      toast.success('Vendor pricing added successfully');
    }
    
    setSelectedVendorId(null);
    setServicePrices([]);
    setTyrePrices([]);
    setIsAddDialogOpen(false);
  };
  
  const Pagination = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
    
    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    
    return (
      <div className="flex items-center justify-center mt-4 space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
        >
          Previous
        </Button>
        
        {startPage > 1 && (
          <>
            <Button variant="outline" size="sm" onClick={() => handlePageChange(1)}>1</Button>
            {startPage > 2 && <span className="px-2">...</span>}
          </>
        )}
        
        {pageNumbers.map(number => (
          <Button
            key={number}
            variant={number === currentPage ? "default" : "outline"}
            size="sm"
            onClick={() => handlePageChange(number)}
          >
            {number}
          </Button>
        ))}
        
        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && <span className="px-2">...</span>}
            <Button variant="outline" size="sm" onClick={() => handlePageChange(totalPages)}>
              {totalPages}
            </Button>
          </>
        )}
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next
        </Button>
      </div>
    );
  };
  
  const renderTableView = () => {
    return (
      <div className="space-y-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Vendor</TableHead>
              <TableHead>Services</TableHead>
              <TableHead>Tyre Types</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentPageData.map((mapping) => (
              <TableRow key={mapping.id}>
                <TableCell className="font-medium">{mapping.vendorName}</TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {mapping.servicePrices.map((service) => {
                      const difference = service.vendorPrice - service.basePrice;
                      const percentDiff = ((difference / service.basePrice) * 100).toFixed(1);
                      
                      return (
                        <div key={service.serviceId} className="flex justify-between text-sm">
                          <span>{service.serviceName}</span>
                          <div className="flex flex-col items-end">
                            <span className={difference < 0 ? 'text-green-600' : difference > 0 ? 'text-red-600' : ''}>
                              ${service.vendorPrice.toFixed(2)} 
                              {difference !== 0 && ` (${difference > 0 ? '+' : ''}${percentDiff}%)`}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Tax: {service.taxRate}% | Rules: {service.pricingRules.length}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {mapping.tyrePrices.map((tyre) => {
                      const difference = tyre.vendorPrice - tyre.basePrice;
                      const percentDiff = ((difference / tyre.basePrice) * 100).toFixed(1);
                      
                      return (
                        <div key={tyre.tyreTypeId} className="flex justify-between text-sm">
                          <span>{tyre.tyreTypeName}</span>
                          <div className="flex flex-col items-end">
                            <span className={difference < 0 ? 'text-green-600' : difference > 0 ? 'text-red-600' : ''}>
                              ${tyre.vendorPrice.toFixed(2)}
                              {difference !== 0 && ` (${difference > 0 ? '+' : ''}${percentDiff}%)`}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Tax: {tyre.taxRate}% | Rules: {tyre.pricingRules.length}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setSelectedVendorId(mapping.vendorId);
                      setServicePrices(mapping.servicePrices);
                      setTyrePrices(mapping.tyrePrices);
                      setIsAddDialogOpen(true);
                    }}
                  >
                    Edit
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {totalItems > 0 && <Pagination />}
      </div>
    );
  };
  
  const renderCardView = () => {
    return (
      <div className="space-y-6">
        {currentPageData.map((mapping) => (
          <Card key={mapping.id}>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">{mapping.vendorName}</h3>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setSelectedVendorId(mapping.vendorId);
                    setServicePrices(mapping.servicePrices);
                    setTyrePrices(mapping.tyrePrices);
                    setIsAddDialogOpen(true);
                  }}
                >
                  Edit Pricing
                </Button>
              </div>
              
              <Tabs defaultValue="services">
                <TabsList>
                  <TabsTrigger value="services">Services</TabsTrigger>
                  <TabsTrigger value="tyres">Tyre Types</TabsTrigger>
                </TabsList>
                <TabsContent value="services" className="pt-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Service</TableHead>
                        <TableHead className="text-right">Base Price</TableHead>
                        <TableHead className="text-right">Vendor Price</TableHead>
                        <TableHead className="text-right">Tax Rate</TableHead>
                        <TableHead className="text-right">Pricing Rules</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mapping.servicePrices.map((service) => {
                        const difference = service.vendorPrice - service.basePrice;
                        const percentDiff = ((difference / service.basePrice) * 100).toFixed(1);
                        
                        return (
                          <TableRow key={service.serviceId}>
                            <TableCell>{service.serviceName}</TableCell>
                            <TableCell className="text-right">${service.basePrice.toFixed(2)}</TableCell>
                            <TableCell className="text-right">
                              <span className={difference < 0 ? 'text-green-600' : difference > 0 ? 'text-red-600' : ''}>
                                ${service.vendorPrice.toFixed(2)}
                                {difference !== 0 && ` (${difference > 0 ? '+' : ''}${difference.toFixed(2)})`}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">{service.taxRate}%</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-1 flex-wrap">
                                {service.pricingRules.length > 0 ? (
                                  service.pricingRules.map((rule) => (
                                    <RuleBadge key={rule.id} rule={rule} highlighted />
                                  ))
                                ) : (
                                  <span className="text-muted-foreground text-sm">None</span>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TabsContent>
                <TabsContent value="tyres" className="pt-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tyre Type</TableHead>
                        <TableHead className="text-right">Base Price</TableHead>
                        <TableHead className="text-right">Vendor Price</TableHead>
                        <TableHead className="text-right">Tax Rate</TableHead>
                        <TableHead className="text-right">Pricing Rules</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mapping.tyrePrices.map((tyre) => {
                        const difference = tyre.vendorPrice - tyre.basePrice;
                        const percentDiff = ((difference / tyre.basePrice) * 100).toFixed(1);
                        
                        return (
                          <TableRow key={tyre.tyreTypeId}>
                            <TableCell>{tyre.tyreTypeName}</TableCell>
                            <TableCell className="text-right">${tyre.basePrice.toFixed(2)}</TableCell>
                            <TableCell className="text-right">
                              <span className={difference < 0 ? 'text-green-600' : difference > 0 ? 'text-red-600' : ''}>
                                ${tyre.vendorPrice.toFixed(2)}
                                {difference !== 0 && ` (${difference > 0 ? '+' : ''}${difference.toFixed(2)})`}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">{tyre.taxRate}%</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-1 flex-wrap">
                                {tyre.pricingRules.length > 0 ? (
                                  tyre.pricingRules.map((rule) => (
                                    <RuleBadge key={rule.id} rule={rule} highlighted />
                                  ))
                                ) : (
                                  <span className="text-muted-foreground text-sm">None</span>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ))}
        
        {totalItems > 0 && <Pagination />}
      </div>
    );
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Vendor Pricing</h1>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Set Vendor Pricing
        </Button>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search vendor..."
            value={searchTerm}
            onChange={handleSearch}
            className="pl-8"
          />
        </div>
        
        <div className="flex items-center gap-4">
          <PageSizeSelector 
            pageSize={pageSize}
            onPageSizeChange={handlePageSizeChange}
            options={[5, 10, 25, 50]}
          />
          
          <div className="border rounded-md flex overflow-hidden">
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-none"
              onClick={() => setViewMode('table')}
            >
              <TableIcon className="h-4 w-4 mr-1" /> Table
            </Button>
            <Button
              variant={viewMode === 'card' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-none"
              onClick={() => setViewMode('card')}
            >
              <LayoutGrid className="h-4 w-4 mr-1" /> Cards
            </Button>
          </div>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="text-muted-foreground">Loading pricing data...</div>
        </div>
      ) : (
        <>
          {filteredPriceMappings.length === 0 ? (
            <div className="border rounded-lg flex flex-col items-center justify-center p-12 text-center">
              <div className="text-muted-foreground mb-2">No vendor pricing found</div>
              <Button 
                variant="outline"
                onClick={() => setIsAddDialogOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Vendor Pricing
              </Button>
            </div>
          ) : (
            viewMode === 'table' ? renderTableView() : renderCardView()
          )}
        </>
      )}
      
      {/* Vendor Pricing Dialog */}
      <VendorPricingDialog
        isOpen={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        vendors={vendors}
        selectedVendorId={selectedVendorId}
        onVendorSelect={handleVendorSelect}
        servicePrices={servicePrices}
        tyrePrices={tyrePrices}
        onServicePriceChange={handleServicePriceChange}
        onTyrePriceChange={handleTyrePriceChange}
        onTaxRateChange={handleTaxRateChange}
        onPricingRuleToggle={handlePricingRuleToggle}
        onOpenNewRuleDialog={handleOpenNewRuleDialog}
        onSave={handleSavePricing}
        pricingRules={pricingRules}
      />
      
      {/* New Pricing Rule Dialog */}
      <Dialog open={isNewRuleDialogOpen} onOpenChange={setIsNewRuleDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Pricing Rule</DialogTitle>
            <DialogDescription>
              {currentEditingItem ? `Add a new pricing rule for ${currentEditingItem.name}.` : 'Add a new pricing rule.'}
            </DialogDescription>
          </DialogHeader>
          <PricingRuleForm 
            form={newRuleForm} 
            onSubmit={handleCreateNewRule}
            editingItemName={currentEditingItem?.name}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VendorPricing;
