export interface Location {
  latitude: number;
  longitude: number;
}

export interface Vendor {
  id: number;
  name: string;
  contactNumber: string;
  serviceDescription: string;
  averageRating: number;
  totalReviews: number;
  location: Location;
  isActive: boolean;
  isOnline: boolean;
  email: string;
}

export interface CreateVendorRequest {
  name: string;
  contactNumber: string;
  serviceDescription: string;
  location: Location;
  email: string;
  isActive?: boolean;
}
