import { apiRequest } from '../core/apiClient';
import { Service, CreateServiceRequest } from '../models/service';

// Service-specific API methods
export const serviceApi = {
  // Get all services
  getAllServices: async (): Promise<Service[]> => {
    return apiRequest<Service[]>('/api/Service/GetAllServices');
  },

  // Create a new service
  createService: async (serviceData: CreateServiceRequest): Promise<Service> => {
    return apiRequest<Service>('/api/Service/Create', 'POST', serviceData);
  },

  // Delete a service
  deleteService: async (id: number): Promise<void> => {
    return apiRequest<void>(`/api/Service/Delete?id=${id}`, 'DELETE');
  }
};
