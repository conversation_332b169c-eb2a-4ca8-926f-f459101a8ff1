
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tag } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export interface PricingRule {
  id: number;
  name: string;
  type: 'percentage' | 'fixed';
  value: number;
  condition?: string;
  minAmount?: number;
  maxAmount?: number;
}

interface RuleBadgeProps {
  rule: PricingRule;
  highlighted?: boolean;
}

export const RuleBadge: React.FC<RuleBadgeProps> = ({ rule, highlighted = false }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant="outline" 
            className={`whitespace-nowrap flex items-center gap-1 cursor-help ${highlighted ? 'bg-blue-50 border-blue-300' : ''}`}
          >
            <Tag className="h-3 w-3" /> {rule.name}
          </Badge>
        </TooltipTrigger>
        <TooltipContent className="p-3 max-w-[300px] bg-white shadow-lg">
          <div className="space-y-1">
            <p className="font-medium text-sm">{rule.name}</p>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Type:</span>
                <span className="font-medium">{rule.type === 'percentage' ? 'Percentage' : 'Fixed Amount'}</span>
              </div>
              <div className="flex justify-between">
                <span>Value:</span>
                <span className="font-medium">
                  {rule.type === 'percentage' ? `${rule.value}%` : `$${rule.value.toFixed(2)}`}
                </span>
              </div>
              {rule.condition && (
                <div className="flex justify-between">
                  <span>Condition:</span>
                  <span className="font-medium">{rule.condition}</span>
                </div>
              )}
              {rule.minAmount && (
                <div className="flex justify-between">
                  <span>Min Amount:</span>
                  <span className="font-medium">${rule.minAmount.toFixed(2)}</span>
                </div>
              )}
              {rule.maxAmount && (
                <div className="flex justify-between">
                  <span>Max Amount:</span>
                  <span className="font-medium">${rule.maxAmount.toFixed(2)}</span>
                </div>
              )}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
