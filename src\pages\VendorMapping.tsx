
import React, { useState, useEffect } from 'react';
import { 
  Table, TableBody, TableCaption, TableCell, TableHead, 
  TableHeader, TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Dialog, DialogContent, DialogDescription, DialogFooter, 
  DialogHeader, DialogTitle, DialogTrigger, DialogClose 
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { 
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue 
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Filter } from 'lucide-react';
import { api } from '@/services/api';
import { toast } from 'sonner';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';

interface Vendor {
  id: number;
  name: string;
  email: string;
  status: 'approved' | 'pending' | 'rejected';
}

interface Service {
  id: number;
  name: string;
  description: string;
  price: number;
  estimatedTime: string;
  status: string;
}

interface TyreType {
  id: number;
  name: string;
  description: string;
  sizes: string[];
}

interface VendorMapping {
  id: number;
  vendorId: number;
  vendorName: string;
  serviceIds: number[];
  tyreTypeIds: number[];
}

const VendorMapping: React.FC = () => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [tyreTypes, setTyreTypes] = useState<TyreType[]>([]);
  const [mappings, setMappings] = useState<VendorMapping[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  
  // New mapping form state
  const [newMapping, setNewMapping] = useState<{
    vendorId: number | null;
    serviceIds: number[];
    tyreTypeIds: number[];
  }>({
    vendorId: null,
    serviceIds: [],
    tyreTypeIds: []
  });
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // In a real app, these would be API calls
        const vendorsData = api.mockData.getVendors();
        const servicesData = api.mockData.getServices();
        const tyreTypesData = api.mockData.getTyreTypes();
        
        // Type casting for vendors
        const typedVendors = vendorsData.map(vendor => ({
          id: vendor.id,
          name: vendor.name,
          email: vendor.email,
          status: vendor.status as 'approved' | 'pending' | 'rejected'
        }));
        
        setVendors(typedVendors);
        setServices(servicesData);
        setTyreTypes(tyreTypesData);
        
        // Generate some mock mappings
        const mockMappings: VendorMapping[] = [
          { 
            id: 1, 
            vendorId: 1, 
            vendorName: 'Tyre Express', 
            serviceIds: [1, 2], 
            tyreTypeIds: [1, 3] 
          },
          { 
            id: 2, 
            vendorId: 3, 
            vendorName: 'Road Assist', 
            serviceIds: [1, 3, 5], 
            tyreTypeIds: [2, 4] 
          },
          { 
            id: 3, 
            vendorId: 4, 
            vendorName: 'Tyre Hub', 
            serviceIds: [2, 4], 
            tyreTypeIds: [1, 2, 5] 
          }
        ];
        
        setMappings(mockMappings);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to fetch data');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  const filteredMappings = mappings.filter(mapping => 
    mapping.vendorName.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleServiceSelect = (serviceId: number) => {
    const isSelected = newMapping.serviceIds.includes(serviceId);
    
    if (isSelected) {
      setNewMapping({
        ...newMapping,
        serviceIds: newMapping.serviceIds.filter(id => id !== serviceId)
      });
    } else {
      setNewMapping({
        ...newMapping,
        serviceIds: [...newMapping.serviceIds, serviceId]
      });
    }
  };
  
  const handleTyreTypeSelect = (tyreTypeId: number) => {
    const isSelected = newMapping.tyreTypeIds.includes(tyreTypeId);
    
    if (isSelected) {
      setNewMapping({
        ...newMapping,
        tyreTypeIds: newMapping.tyreTypeIds.filter(id => id !== tyreTypeId)
      });
    } else {
      setNewMapping({
        ...newMapping,
        tyreTypeIds: [...newMapping.tyreTypeIds, tyreTypeId]
      });
    }
  };
  
  const handleAddMapping = () => {
    if (!newMapping.vendorId) {
      toast.error('Please select a vendor');
      return;
    }
    
    if (newMapping.serviceIds.length === 0 && newMapping.tyreTypeIds.length === 0) {
      toast.error('Please select at least one service or tyre type');
      return;
    }
    
    const selectedVendor = vendors.find(v => v.id === newMapping.vendorId);
    
    if (!selectedVendor) {
      toast.error('Selected vendor not found');
      return;
    }
    
    // Check if this vendor already has a mapping
    const existingMappingIndex = mappings.findIndex(m => m.vendorId === newMapping.vendorId);
    
    if (existingMappingIndex !== -1) {
      // Update existing mapping
      const updatedMappings = [...mappings];
      updatedMappings[existingMappingIndex] = {
        ...updatedMappings[existingMappingIndex],
        serviceIds: newMapping.serviceIds,
        tyreTypeIds: newMapping.tyreTypeIds
      };
      
      setMappings(updatedMappings);
      toast.success('Vendor mapping updated successfully');
    } else {
      // Create new mapping
      const newId = mappings.length > 0 ? Math.max(...mappings.map(m => m.id)) + 1 : 1;
      
      const mapping: VendorMapping = {
        id: newId,
        vendorId: newMapping.vendorId,
        vendorName: selectedVendor.name,
        serviceIds: newMapping.serviceIds,
        tyreTypeIds: newMapping.tyreTypeIds
      };
      
      setMappings([...mappings, mapping]);
      toast.success('Vendor mapping added successfully');
    }
    
    setNewMapping({
      vendorId: null,
      serviceIds: [],
      tyreTypeIds: []
    });
    
    setIsAddDialogOpen(false);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Vendor Mapping</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Map Vendor
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Map Vendor to Services & Tyre Types</DialogTitle>
              <DialogDescription>
                Associate a vendor with services they can provide and tyre types they can handle.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="vendor-select">Select Vendor</Label>
                  <Select
                    value={newMapping.vendorId?.toString() || ""}
                    onValueChange={(value) => setNewMapping({ ...newMapping, vendorId: parseInt(value) })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a vendor" />
                    </SelectTrigger>
                    <SelectContent>
                      {vendors
                        .filter(vendor => vendor.status === 'approved')
                        .map((vendor) => (
                          <SelectItem key={vendor.id} value={vendor.id.toString()}>
                            {vendor.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <Tabs defaultValue="services" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="services">Services</TabsTrigger>
                    <TabsTrigger value="tyres">Tyre Types</TabsTrigger>
                  </TabsList>
                  <TabsContent value="services" className="pt-4">
                    <div className="space-y-2">
                      {services.map((service) => (
                        <div key={service.id} className="flex items-center space-x-2">
                          <Checkbox 
                            id={`service-${service.id}`}
                            checked={newMapping.serviceIds.includes(service.id)}
                            onCheckedChange={() => handleServiceSelect(service.id)}
                          />
                          <label
                            htmlFor={`service-${service.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {service.name} - {service.description}
                          </label>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="tyres" className="pt-4">
                    <div className="space-y-2">
                      {tyreTypes.map((tyreType) => (
                        <div key={tyreType.id} className="flex items-center space-x-2">
                          <Checkbox 
                            id={`tyre-${tyreType.id}`}
                            checked={newMapping.tyreTypeIds.includes(tyreType.id)}
                            onCheckedChange={() => handleTyreTypeSelect(tyreType.id)}
                          />
                          <label
                            htmlFor={`tyre-${tyreType.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {tyreType.name} - {tyreType.description}
                          </label>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddMapping}>Save Mapping</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search vendors..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableCaption>List of vendor mappings with services and tyre types.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Vendor</TableHead>
              <TableHead>Services</TableHead>
              <TableHead>Tyre Types</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-10">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-puncture-primary"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredMappings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-10">
                  No vendor mappings found.
                </TableCell>
              </TableRow>
            ) : (
              filteredMappings.map((mapping) => (
                <TableRow key={mapping.id}>
                  <TableCell className="font-medium">{mapping.vendorName}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {mapping.serviceIds.map(serviceId => {
                        const service = services.find(s => s.id === serviceId);
                        return service ? (
                          <Badge key={serviceId} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            {service.name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {mapping.tyreTypeIds.map(tyreTypeId => {
                        const tyreType = tyreTypes.find(t => t.id === tyreTypeId);
                        return tyreType ? (
                          <Badge key={tyreTypeId} variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                            {tyreType.name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // Pre-fill the form for editing
                        setNewMapping({
                          vendorId: mapping.vendorId,
                          serviceIds: mapping.serviceIds,
                          tyreTypeIds: mapping.tyreTypeIds
                        });
                        setIsAddDialogOpen(true);
                      }}
                    >
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default VendorMapping;
