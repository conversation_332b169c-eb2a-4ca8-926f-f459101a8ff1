import React, { useState } from 'react';
import PricingManagement from './PricingManagement';
import VendorPricingManagement from './VendorPricingManagement';

const PricingApp: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<'pricing' | 'vendor-pricing'>('pricing');

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-gray-900">Admin Dashboard</h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <button
                  onClick={() => setCurrentScreen('pricing')}
                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                    currentScreen === 'pricing'
                      ? 'border-blue-500 text-gray-900'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Pricing Management
                </button>
                <button
                  onClick={() => setCurrentScreen('vendor-pricing')}
                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                    currentScreen === 'vendor-pricing'
                      ? 'border-blue-500 text-gray-900'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Vendor Pricing Management
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div className="sm:hidden">
        <div className="pt-2 pb-3 space-y-1 bg-white border-b border-gray-200">
          <button
            onClick={() => setCurrentScreen('pricing')}
            className={`block pl-3 pr-4 py-2 text-base font-medium w-full text-left ${
              currentScreen === 'pricing'
                ? 'bg-blue-50 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            Pricing Management
          </button>
          <button
            onClick={() => setCurrentScreen('vendor-pricing')}
            className={`block pl-3 pr-4 py-2 text-base font-medium w-full text-left ${
              currentScreen === 'vendor-pricing'
                ? 'bg-blue-50 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            Vendor Pricing Management
          </button>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto">
        {currentScreen === 'pricing' && <PricingManagement />}
        {currentScreen === 'vendor-pricing' && <VendorPricingManagement />}
      </main>
    </div>
  );
};

export default PricingApp;
