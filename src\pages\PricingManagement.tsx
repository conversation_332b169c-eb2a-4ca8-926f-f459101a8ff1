import React, { useState, useEffect } from 'react';
import { Search, Plus, Edit, Trash2, ArrowLeft } from 'lucide-react';

// Mock data
const mockPricingData = [
  {
    id: 1,
    name: "Standard Cleaning Service",
    unitType: "hour",
    basePrice: 500.00,
    taxRate: 18.00,
    serviceCost: 50.00,
    platformCommissionPercentage: 15.00,
    ownerMargin: {
      isPercentage: true,
      value: 10.00,
      applyOwnerMargin: true
    }
  },
  {
    id: 2,
    name: "Premium Tyre Repair",
    unitType: "item",
    basePrice: 300.00,
    taxRate: 12.00,
    serviceCost: 25.00,
    platformCommissionPercentage: 10.00,
    ownerMargin: {
      isPercentage: false,
      value: 50.00,
      applyOwnerMargin: true
    }
  },
  {
    id: 3,
    name: "Fuel Delivery",
    unitType: "liter",
    basePrice: 80.00,
    taxRate: 5.00,
    serviceCost: 15.00,
    platformCommissionPercentage: 8.00,
    ownerMargin: {
      isPercentage: true,
      value: 15.00,
      applyOwnerMargin: false
    }
  }
];

const unitTypes = ["kg", "liter", "hour", "sq ft", "item"];

interface PricingEntity {
  id?: number;
  name: string;
  unitType: string;
  basePrice: number;
  taxRate: number;
  serviceCost: number;
  platformCommissionPercentage: number;
  ownerMargin: {
    isPercentage: boolean;
    value: number;
    applyOwnerMargin: boolean;
  };
}

const PricingManagement: React.FC = () => {
  const [mode, setMode] = useState<'table' | 'create' | 'edit'>('table');
  const [pricingData, setPricingData] = useState<PricingEntity[]>(mockPricingData);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [editingItem, setEditingItem] = useState<PricingEntity | null>(null);
  const itemsPerPage = 5;

  const [formData, setFormData] = useState<PricingEntity>({
    name: '',
    unitType: 'hour',
    basePrice: 0,
    taxRate: 0,
    serviceCost: 0,
    platformCommissionPercentage: 0,
    ownerMargin: {
      isPercentage: true,
      value: 0,
      applyOwnerMargin: true
    }
  });

  // Filter data based on search term
  const filteredData = pricingData.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

  const handleCreate = () => {
    setFormData({
      name: '',
      unitType: 'hour',
      basePrice: 0,
      taxRate: 0,
      serviceCost: 0,
      platformCommissionPercentage: 0,
      ownerMargin: {
        isPercentage: true,
        value: 0,
        applyOwnerMargin: true
      }
    });
    setMode('create');
  };

  const handleEdit = (item: PricingEntity) => {
    setFormData(item);
    setEditingItem(item);
    setMode('edit');
  };

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this pricing entity?')) {
      setPricingData(prev => prev.filter(item => item.id !== id));
    }
  };

  const handleSave = () => {
    if (mode === 'create') {
      const newId = Math.max(...pricingData.map(p => p.id || 0)) + 1;
      setPricingData(prev => [...prev, { ...formData, id: newId }]);
    } else if (mode === 'edit' && editingItem) {
      setPricingData(prev => prev.map(item => 
        item.id === editingItem.id ? { ...formData, id: editingItem.id } : item
      ));
    }
    setMode('table');
    setEditingItem(null);
  };

  const handleCancel = () => {
    setMode('table');
    setEditingItem(null);
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('ownerMargin.')) {
      const marginField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        ownerMargin: {
          ...prev.ownerMargin,
          [marginField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  if (mode === 'create' || mode === 'edit') {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="mb-6">
          <button
            onClick={handleCancel}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Pricing List
          </button>
          <h1 className="text-2xl font-bold text-gray-900">
            {mode === 'create' ? 'Create New Pricing Entity' : 'Edit Pricing Entity'}
          </h1>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <form onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
            {/* Base Price Info */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Base Price Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Unit Type *
                  </label>
                  <select
                    value={formData.unitType}
                    onChange={(e) => handleInputChange('unitType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {unitTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base Price *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.basePrice}
                    onChange={(e) => handleInputChange('basePrice', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tax Rate (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.taxRate}
                    onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Service Cost
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.serviceCost}
                    onChange={(e) => handleInputChange('serviceCost', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Platform Commission (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.platformCommissionPercentage}
                    onChange={(e) => handleInputChange('platformCommissionPercentage', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Owner Margin */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Owner Margin</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Is Percentage
                  </label>
                  <select
                    value={formData.ownerMargin.isPercentage.toString()}
                    onChange={(e) => handleInputChange('ownerMargin.isPercentage', e.target.value === 'true')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="true">True</option>
                    <option value="false">False</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Value
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.ownerMargin.value}
                    onChange={(e) => handleInputChange('ownerMargin.value', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Apply Owner Margin
                  </label>
                  <select
                    value={formData.ownerMargin.applyOwnerMargin.toString()}
                    onChange={(e) => handleInputChange('ownerMargin.applyOwnerMargin', e.target.value === 'true')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="true">True</option>
                    <option value="false">False</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-gray-900">Pricing Management</h1>
          <button
            onClick={handleCreate}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create
          </button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search by name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full md:w-96 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Base Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tax Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Platform Commission %
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedData.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.unitType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${item.basePrice.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.taxRate}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.platformCommissionPercentage}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(item)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(item.id!)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(startIndex + itemsPerPage, filteredData.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredData.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === currentPage
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PricingManagement;
