
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DollarSign, PlusCircle } from 'lucide-react';
import { Checkbox } from "@/components/ui/checkbox";
import { PricingRule } from './RuleBadge';
import { RuleBadge } from './RuleBadge';

interface PriceItemEditorProps {
  itemType: 'service' | 'tyre';
  itemId: number;
  itemName: string;
  basePrice: number;
  vendorPrice: number;
  taxRate: number;
  pricingRules: PricingRule[];
  availableRules: PricingRule[];
  onVendorPriceChange: (value: number) => void;
  onTaxRateChange: (value: number) => void;
  onPricingRuleToggle: (ruleId: number, checked: boolean) => void;
  onAddNewRule: () => void;
}

export const PriceItemEditor: React.FC<PriceItemEditorProps> = ({
  itemType,
  itemId,
  itemName,
  basePrice,
  vendorPrice,
  taxRate,
  pricingRules,
  availableRules,
  onVendorPriceChange,
  onTaxRateChange,
  onPricingRuleToggle,
  onAddNewRule
}) => {
  return (
    <div className="border rounded-md p-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex-1">
          <Label>Name</Label>
          <div className="font-medium">{itemName}</div>
          <div className="text-sm text-muted-foreground mt-1">
            Base Price: ${basePrice.toFixed(2)}
          </div>
        </div>
        
        <div className="w-full sm:w-32">
          <Label htmlFor={`${itemType}-price-${itemId}`}>Vendor Price</Label>
          <div className="flex mt-1">
            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground">
              <DollarSign className="h-4 w-4" />
            </span>
            <Input
              id={`${itemType}-price-${itemId}`}
              type="number"
              value={vendorPrice}
              onChange={(e) => onVendorPriceChange(parseFloat(e.target.value))}
              className="rounded-l-none"
              step="0.01"
            />
          </div>
        </div>
        
        <div className="w-full sm:w-24">
          <Label htmlFor={`${itemType}-tax-${itemId}`}>Tax Rate (%)</Label>
          <Input
            id={`${itemType}-tax-${itemId}`}
            type="number"
            value={taxRate}
            onChange={(e) => onTaxRateChange(parseFloat(e.target.value))}
            className="mt-1"
          />
        </div>
      </div>
      
      <div className="mt-4">
        <div className="flex justify-between items-center mb-2">
          <Label>Applied Pricing Rules</Label>
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={onAddNewRule}
          >
            <PlusCircle className="h-3.5 w-3.5 mr-1" /> Add Rule
          </Button>
        </div>
        <div className="border rounded-md p-3 space-y-2">
          {availableRules.length > 0 ? (
            availableRules.map(rule => {
              const isChecked = pricingRules.some(r => r.id === rule.id);
              return (
                <div key={rule.id} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`rule-${itemId}-${rule.id}`}
                    checked={isChecked}
                    onCheckedChange={(checked) => onPricingRuleToggle(rule.id, Boolean(checked))}
                  />
                  <Label 
                    htmlFor={`rule-${itemId}-${rule.id}`}
                    className="flex items-center gap-2 cursor-pointer text-sm"
                  >
                    <RuleBadge rule={rule} highlighted={isChecked} />
                  </Label>
                </div>
              );
            })
          ) : (
            <div className="text-muted-foreground text-sm">No pricing rules defined</div>
          )}
        </div>
      </div>
    </div>
  );
};
